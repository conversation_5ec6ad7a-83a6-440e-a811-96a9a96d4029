<?php
/**
 * 测试修复后的评论查询功能
 */

// 数据库配置
$host = 'localhost';
$dbname = 'deshengdulikaifa';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

echo "=== 测试修复后的评论查询功能 ===\n\n";

// 1. 模拟留言列表查询（获取父评论和第一个子评论）
echo "1. 测试留言列表查询...\n";

// 查询父评论
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE parent_id = 0 AND type = 'message' AND status = 'normal' ORDER BY createtime DESC LIMIT 3");
$stmt->execute();
$parentComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($parentComments as $parent) {
    echo "父评论 ID: {$parent['id']}, 内容: {$parent['content']}\n";
    
    // 查询第一个子评论（包含回复信息）
    $stmt = $pdo->prepare("
        SELECT c.*, r.nickname as reply_to_nickname, r.content as reply_to_content 
        FROM fa_comment c 
        LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
        WHERE c.parent_id = ? AND c.type = 'message' AND c.status = 'normal'
        ORDER BY c.createtime DESC 
        LIMIT 1
    ");
    $stmt->execute([$parent['id']]);
    $firstChild = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($firstChild) {
        echo "  第一个子评论 ID: {$firstChild['id']}, 内容: {$firstChild['content']}\n";
        if ($firstChild['reply_to_nickname']) {
            echo "    回复用户: {$firstChild['reply_to_nickname']}\n";
            echo "    回复内容: {$firstChild['reply_to_content']}\n";
        } else {
            echo "    回复类型: 回复父评论\n";
        }
    } else {
        echo "  无子评论\n";
    }
    echo "\n";
}

// 2. 测试子评论列表查询
echo "2. 测试子评论列表查询...\n";

// 选择一个有子评论的父评论
$stmt = $pdo->prepare("SELECT id FROM fa_comment WHERE parent_id = 0 AND type = 'message' AND status = 'normal' ORDER BY createtime DESC LIMIT 1");
$stmt->execute();
$parentId = $stmt->fetchColumn();

if ($parentId) {
    echo "测试父评论 ID: $parentId\n";
    
    // 查询所有子评论（包含回复信息）
    $stmt = $pdo->prepare("
        SELECT c.*, r.nickname as reply_to_nickname, r.content as reply_to_content 
        FROM fa_comment c 
        LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
        WHERE c.parent_id = ? AND c.type = 'message' AND c.status = 'normal'
        ORDER BY c.createtime DESC
    ");
    $stmt->execute([$parentId]);
    $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "子评论列表：\n";
    foreach ($children as $child) {
        echo "  - ID: {$child['id']}, 内容: {$child['content']}\n";
        if ($child['reply_to_nickname']) {
            echo "    回复用户: {$child['reply_to_nickname']}\n";
            echo "    回复内容: {$child['reply_to_content']}\n";
        } else {
            echo "    回复类型: 回复父评论\n";
        }
        echo "\n";
    }
} else {
    echo "没有找到父评论\n";
}

// 3. 测试API接口查询
echo "3. 测试API接口查询...\n";

if ($parentId) {
    // 模拟API接口的查询逻辑
    $page = 1;
    $limit = 5;
    $offset = 1 + ($page - 1) * $limit; // 跳过最早那一条
    
    $stmt = $pdo->prepare("
        SELECT c.*, r.nickname as reply_to_nickname, r.content as reply_to_content 
        FROM fa_comment c 
        LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
        WHERE c.parent_id = ? AND c.type = 'message' AND c.status = 'normal'
        ORDER BY c.createtime DESC 
        LIMIT $offset, $limit
    ");
    $stmt->execute([$parentId]);
    $apiChildren = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "API接口返回的子评论：\n";
    foreach ($apiChildren as $child) {
        echo "  - ID: {$child['id']}, 内容: {$child['content']}\n";
        if ($child['reply_to_nickname']) {
            echo "    回复用户: {$child['reply_to_nickname']}\n";
            echo "    回复内容: {$child['reply_to_content']}\n";
        } else {
            echo "    回复类型: 回复父评论\n";
        }
        echo "\n";
    }
}

echo "✓ 测试完成！\n";
?> 