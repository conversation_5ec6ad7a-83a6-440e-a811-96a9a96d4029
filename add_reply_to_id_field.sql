-- 添加reply_to_id字段到fa_comment表
-- 用于标识回复的是哪个子评论，如果不是回复子评论则为0

ALTER TABLE `fa_comment` 
ADD COLUMN `reply_to_id` int(10) unsigned DEFAULT '0' COMMENT '回复的子评论ID，0表示不是回复子评论' 
AFTER `parent_id`;

-- 添加索引以提高查询性能
ALTER TABLE `fa_comment` 
ADD INDEX `reply_to_id` (`reply_to_id`);

-- 更新现有数据的reply_to_id字段
-- 如果parent_id不为0，说明是子评论，reply_to_id设为0
-- 如果parent_id为0，说明是父评论，reply_to_id也设为0
UPDATE `fa_comment` SET `reply_to_id` = 0; 