# 留言板功能说明

## 功能概述

留言板已经升级为支持父评论和子评论的层级结构，实现了以下功能：

1. **父评论显示**：只显示父评论（parent_id = 0）列表
2. **子评论预览**：每个父评论下方显示第一个子评论
3. **加载更多子评论**：点击"查看全部回复"按钮可以加载更多子评论
4. **回复功能**：可以对任何父评论进行回复
5. **点赞功能**：支持对评论进行点赞/取消点赞

## 数据库结构

### 评论表 (fa_comment)
```sql
CREATE TABLE `fa_comment` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `content` text NOT NULL COMMENT '评论内容',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `website` varchar(255) DEFAULT '' COMMENT '网站',
  `type` enum('article','message','about','links') NOT NULL DEFAULT 'article' COMMENT '评论类型',
  `article_id` int(10) unsigned DEFAULT '0' COMMENT '文章ID',
  `parent_id` int(10) unsigned DEFAULT '0' COMMENT '父评论ID',
  `likes` int(10) unsigned DEFAULT '0' COMMENT '点赞数',
  `is_admin` tinyint(1) unsigned DEFAULT '0' COMMENT '是否管理员回复',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `browser` varchar(100) DEFAULT '' COMMENT '浏览器信息',
  `device` varchar(100) DEFAULT '' COMMENT '设备信息',
  `ip` varchar(45) DEFAULT '' COMMENT 'IP地址',
  `ip_location` varchar(100) DEFAULT '' COMMENT 'IP归属地',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `article_id` (`article_id`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
```

## 新增字段

如果数据库中没有以下字段，请执行以下SQL：

```sql
ALTER TABLE `fa_comment` 
ADD COLUMN `browser` varchar(100) DEFAULT '' COMMENT '浏览器信息' AFTER `website`,
ADD COLUMN `device` varchar(100) DEFAULT '' COMMENT '设备信息' AFTER `browser`,
ADD COLUMN `ip` varchar(45) DEFAULT '' COMMENT 'IP地址' AFTER `device`,
ADD COLUMN `ip_location` varchar(100) DEFAULT '' COMMENT 'IP归属地' AFTER `ip`;
```

## 功能特点

### 1. 父评论列表
- 只显示 `parent_id = 0` 的评论
- 支持按时间排序（最新/最早/最热）
- 分页加载

### 2. 子评论显示
- 每个父评论下方显示第一个子评论
- 显示子评论总数
- 点击"查看全部回复"加载更多子评论

### 3. 回复功能
- 可以对任何父评论进行回复
- 回复会作为子评论保存
- 支持用户信息缓存

### 4. 点赞功能
- 支持对评论进行点赞/取消点赞
- 点赞状态保存在本地存储中
- 防止重复点赞

## API接口

### 1. 获取子评论列表
```
GET /comment/children?parent_id=1&page=1
```

返回格式：
```json
{
  "code": 1,
  "data": {
    "childrenList": [...],
    "total": 10,
    "page": 1,
    "hasMore": true
  }
}
```

### 2. 添加评论/回复
```
POST /api/comment/add
```

参数：
- `type`: 评论类型（message）
- `parent_id`: 父评论ID（回复时使用）
- `nickname`: 昵称
- `email`: 邮箱
- `content`: 评论内容
- `website`: 网站（可选）

### 3. 点赞评论
```
POST /api/comment/like
```

参数：
- `comment_id`: 评论ID

## 前端功能

### 1. 用户信息缓存
- 昵称、邮箱、网站信息会保存在本地存储中
- 下次发表评论时自动填充

### 2. 点赞状态管理
- 点赞状态保存在本地存储中
- 页面刷新后保持点赞状态

### 3. 动态加载
- 支持AJAX动态加载更多评论
- 支持动态加载更多子评论
- 实时更新评论列表

## 样式特点

### 1. 子评论样式
- 左侧有蓝色边框标识
- 背景色为浅灰色
- 头像尺寸较小（32px）
- 字体稍小

### 2. 响应式设计
- 支持移动端和桌面端
- 自适应布局

## 测试方法

1. 运行测试脚本：
```bash
php test_message_functionality.php
```

2. 访问留言板页面：
```
http://localhost:8000/message
```

3. 测试功能：
   - 发表父评论
   - 回复评论（创建子评论）
   - 点赞评论
   - 加载更多子评论
   - 切换排序方式

## 注意事项

1. 确保数据库中有必要的字段
2. 检查路由配置是否正确
3. 确保CommentService类中的所有方法都可用
4. 测试IP归属地API是否正常工作

## 扩展功能

可以考虑添加的功能：
1. 评论审核功能
2. 评论举报功能
3. 评论搜索功能
4. 评论通知功能
5. 评论表情支持 