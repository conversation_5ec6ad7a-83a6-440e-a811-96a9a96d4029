# 德胜独立开发博客

## 项目简介

这是一个基于 FastAdmin 框架开发的个人博客项目，位于 `application/index` 模块下。

## 功能

- 文章发布与管理
- 分类、标签管理
- 留言功能
- 友情链接
- 关于我页面

## 访问路由

- 首页: `/index/index`
- 归档: `/index/archive`
- 分类: `/index/category`
- 标签: `/index/tag`
- 留言: `/index/message`
- 友链: `/index/links`
- 关于: `/index/about`
- 文章详情: `/index/detail/ids/{id}`

## 后端文件

- `application/index/controller/Index.php`: 博客主要逻辑控制器
- `application/index/controller/Comment.php`: 评论逻辑控制器 (如果实现)
- `application/index/model/`: 模型文件目录 (Article, Category, Tag, Comment)
- `application/index/config.php`: 博客相关配置
- `application/index/route.php`: 博客路由配置

## 前端文件

- `application/index/view/layout/blog.html`: 博客的统一布局文件
- `application/index/view/index/`: 存放博客各个页面的视图文件
- `public/assets/css/blog.css`: 博客的自定义样式
- `public/assets/js/blog.js`: 博客的自定义脚本

## 安装与使用

1.  确保你的环境已经成功安装 FastAdmin。
2.  数据库已经通过之前的脚本正确安装并填充了初始数据。
3.  直接访问 `http://yoursite.com/index` 即可看到博客首页。

所有页面均已完成静态实现和布局统一，并使用了正确的 FastAdmin 路由。

## 问题反馈

在使用中有任何问题，请使用以下联系方式联系我们

问答社区: https://ask.fastadmin.net

Github: https://github.com/fastadminnet/fastadmin

Gitee: https://gitee.com/fastadminnet/fastadmin

## 特别鸣谢

感谢以下的项目,排名不分先后

ThinkPHP：http://www.thinkphp.cn

AdminLTE：https://adminlte.io

Bootstrap：http://getbootstrap.com

jQuery：http://jquery.com

Bootstrap-table：https://github.com/wenzhixin/bootstrap-table

Nice-validator: https://validator.niceue.com

SelectPage: https://github.com/TerryZ/SelectPage

Layer: https://layuion.com/layer/

DropzoneJS: https://www.dropzonejs.com


## 版权信息

FastAdmin遵循Apache2开源协议发布，并提供免费使用。

本项目包含的第三方源码和二进制文件之版权信息另行标注。

版权所有Copyright © 2017-2024 by FastAdmin (https://www.fastadmin.net)

All rights reserved。
