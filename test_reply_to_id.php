<?php
/**
 * 测试reply_to_id字段功能
 */

// 数据库配置
$host = 'localhost';
$dbname = 'deshengdulikaifa';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 1. 检查reply_to_id字段是否存在
echo "1. 检查reply_to_id字段...\n";
$stmt = $pdo->query("DESCRIBE fa_comment");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
$hasReplyToId = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'reply_to_id') {
        $hasReplyToId = true;
        echo "✓ reply_to_id字段存在\n";
        break;
    }
}

if (!$hasReplyToId) {
    echo "✗ reply_to_id字段不存在，需要先运行SQL脚本\n";
    exit;
}

// 2. 创建测试数据
echo "\n2. 创建测试数据...\n";

// 创建父评论
$stmt = $pdo->prepare("INSERT INTO fa_comment (content, nickname, email, type, parent_id, reply_to_id, status, createtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
$stmt->execute([
    '这是第一条父评论',
    '用户A',
    '<EMAIL>',
    'message',
    0,
    0,
    'normal',
    time()
]);
$parentId = $pdo->lastInsertId();
echo "✓ 父评论创建成功，ID: $parentId\n";

// 创建第一个子评论
$stmt->execute([
    '这是第一条子评论',
    '用户B',
    '<EMAIL>',
    'message',
    $parentId,
    0,
    'normal',
    time()
]);
$firstChildId = $pdo->lastInsertId();
echo "✓ 第一个子评论创建成功，ID: $firstChildId\n";

// 创建回复第一个子评论的评论
$stmt->execute([
    '这是回复第一个子评论的评论',
    '用户C',
    '<EMAIL>',
    'message',
    $parentId,
    $firstChildId,
    'normal',
    time()
]);
$replyToFirstId = $pdo->lastInsertId();
echo "✓ 回复第一个子评论的评论创建成功，ID: $replyToFirstId\n";

// 创建第二个子评论
$stmt->execute([
    '这是第二条子评论',
    '用户D',
    '<EMAIL>',
    'message',
    $parentId,
    0,
    'normal',
    time()
]);
$secondChildId = $pdo->lastInsertId();
echo "✓ 第二个子评论创建成功，ID: $secondChildId\n";

// 3. 查询并显示测试数据
echo "\n3. 查询测试数据...\n";
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE parent_id = ? ORDER BY createtime ASC");
$stmt->execute([$parentId]);
$children = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "父评论 $parentId 的子评论列表:\n";
foreach ($children as $child) {
    $replyTo = $child['reply_to_id'] > 0 ? " (回复评论ID: {$child['reply_to_id']})" : "";
    echo "  - ID: {$child['id']}, 内容: {$child['content']}, reply_to_id: {$child['reply_to_id']}$replyTo\n";
}

// 4. 测试查询回复特定评论的评论
echo "\n4. 测试查询回复特定评论的评论...\n";
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE reply_to_id = ?");
$stmt->execute([$firstChildId]);
$replies = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "回复评论 $firstChildId 的评论:\n";
foreach ($replies as $reply) {
    echo "  - ID: {$reply['id']}, 内容: {$reply['content']}\n";
}

echo "\n✓ 测试完成！\n";
?> 