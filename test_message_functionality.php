<?php
/**
 * 测试留言板功能
 * 这个脚本用于测试父评论和子评论的功能
 */

// 数据库配置
$host = 'localhost';
$dbname = 'fastadmin';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 留言板功能测试 ===\n\n";
    
    // 1. 测试创建父评论
    echo "1. 测试创建父评论...\n";
    $parentContent = '这是一条父评论，测试留言板功能';
    $stmt = $pdo->prepare("INSERT INTO fa_comment (content, nickname, email, type, parent_id, browser, device, ip, ip_location, status, createtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $parentContent,
        '测试用户',
        '<EMAIL>',
        'message',
        0,
        'Chrome 91.0',
        'Windows 10',
        '127.0.0.1',
        '本地',
        'normal',
        time()
    ]);
    
    $parentId = $pdo->lastInsertId();
    echo "✓ 父评论创建成功，ID: {$parentId}\n\n";
    
    // 2. 测试创建子评论
    echo "2. 测试创建子评论...\n";
    $stmt = $pdo->prepare("INSERT INTO fa_comment (content, nickname, email, type, parent_id, browser, device, ip, ip_location, status, createtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        '这是对父评论的回复',
        '回复用户',
        '<EMAIL>',
        'message',
        $parentId,
        'Firefox 89.0',
        'macOS',
        '127.0.0.1',
        '本地',
        'normal',
        time()
    ]);
    echo "✓ 子评论创建成功\n\n";
    
    // 3. 测试创建第二个子评论
    echo "3. 测试创建第二个子评论...\n";
    $stmt->execute([
        '这是第二条回复',
        '另一个用户',
        '<EMAIL>',
        'message',
        $parentId,
        'Safari 14.0',
        'iOS 14',
        '127.0.0.1',
        '本地',
        'normal',
        time()
    ]);
    echo "✓ 第二个子评论创建成功\n\n";
    
    // 4. 测试查询父评论列表
    echo "4. 测试查询父评论列表...\n";
    $stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE type = 'message' AND status = 'normal' AND parent_id = 0 ORDER BY createtime DESC");
    $stmt->execute();
    $parentComments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "找到 " . count($parentComments) . " 条父评论\n";
    
    foreach ($parentComments as $comment) {
        echo "  父评论ID: {$comment['id']}, 内容: {$comment['content']}\n";
        
        // 查询子评论数量
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM fa_comment WHERE type = 'message' AND status = 'normal' AND parent_id = ?");
        $stmt->execute([$comment['id']]);
        $childrenCount = $stmt->fetchColumn();
        
        echo "    子评论数量: {$childrenCount}\n";
        
        // 查询第一个子评论
        $stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE type = 'message' AND status = 'normal' AND parent_id = ? ORDER BY createtime ASC LIMIT 1");
        $stmt->execute([$comment['id']]);
        $firstChild = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($firstChild) {
            echo "    第一个子评论: {$firstChild['content']}\n";
        }
        echo "\n";
    }
    
    // 5. 测试查询子评论列表
    echo "5. 测试查询子评论列表...\n";
    $stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE type = 'message' AND status = 'normal' AND parent_id = ? ORDER BY createtime ASC");
    $stmt->execute([$parentId]);
    $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "父评论 {$parentId} 的子评论列表:\n";
    foreach ($children as $child) {
        echo "  子评论ID: {$child['id']}, 内容: {$child['content']}\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "请访问 http://localhost:8000/message 查看留言板效果\n";
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
} 