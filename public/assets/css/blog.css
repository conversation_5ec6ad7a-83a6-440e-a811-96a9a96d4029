/* 博客系统样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f6fa;
}

.main-content {
    margin-top: 76px; /* 为固定导航栏留出空间 */
}

/* Banner区域样式 */
.banner-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 0;
    margin-bottom: 30px;
    min-height: 550px;
    display: flex;
    align-items: center;
}

.banner-content h1 {
    font-weight: 700;
    margin-bottom: 20px;
}

.banner-stats .stat-item h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.banner-stats .stat-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 文章列表样式 */
.article-list .card {
    margin-bottom: 30px;
}

.article-list .card-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.article-list .card-title a {
    color: #333;
    text-decoration: none;
}

.article-list .card-title a:hover {
    color: #007bff;
}

.article-meta {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.article-tags .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

/* 标签云样式 */
.tag-cloud {
    text-align: center;
    /* padding: 1.5rem 0 0.5rem 0; */
    /* line-height: 2.2; */
    /* min-height: 120px; */
}

.tag-cloud a {
    color: #7b8a9a;
    /* margin: 0 8px 8px 0; */
    display: inline-block;
    transition: color 0.2s, font-size 0.2s;
    text-decoration: none;
}

.tag-cloud a:hover {
    color: #005bea;
    text-decoration: underline;
}

/* 推荐文章样式 */
.recommend-article h6 a {
    color: #333;
    text-decoration: none;
}

.recommend-article h6 a:hover {
    color: #007bff;
}

/* 广告位样式 */
.ad-banner {
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 5px;
}

/* 回到顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 25px;
    right: 25px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 9999; /* 确保在最上层 */
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

/* 分页样式 */
.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 700;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 5px;
    transition: color 0.2s ease;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
}

/* 底部样式 */
.footer {
    color: #bdc3c7;
    padding: 2.5rem 0;
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: center;
}

.footer a {
    /* color: #bdc3c7; */
    text-decoration: none;
    transition: color 0.2s;
}

.footer a:hover {
    color: #fff;
    text-decoration: underline;
}

.footer p {
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .banner-section {
        padding: 30px 0;
        min-height: 400px;
    }
    
    .banner-content h1 {
        font-size: 2rem;
    }
    
    .banner-stats .stat-item h3 {
        font-size: 2rem;
    }
    
    .main-content {
        margin-top: 66px;
    }
    
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* 评论系统样式 */
.comment-section {
    margin-top: 30px;
}

.comment-item {
    /* border-left: 3px solid #007bff; */
    padding-left: 15px;
    margin-bottom: 20px;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-author {
    font-weight: 600;
    color: #007bff;
}

.comment-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.comment-content {
    line-height: 1.6;
    margin-bottom: 10px;
}

.comment-actions {
    display: flex;
    gap: 15px;
}

.comment-like {
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s ease;
}

.comment-like:hover {
    color: #dc3545;
}

.comment-like.liked {
    color: #dc3545;
}

.comment-reply {
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s ease;
}

.comment-reply:hover {
    color: #007bff;
}

.comment-children {
    margin-left: 30px;
    margin-top: 15px;
}

/* 表情选择器样式 */
.emoji-picker {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
}

.emoji-item {
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.emoji-item:hover {
    background-color: #f8f9fa;
}

/* Custom Blog Styles */
.content {
    min-height: calc(100vh - 180px); /* 100% view height minus footer and nav */
}

/* Sidebar styles */
.sidebar-card {
    margin-bottom: 1.5rem;
}

/* Article list styles */
.article-item {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.article-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,.1);
}

.article-item .card-footer {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Archive page styles */
.archive-timeline {
    position: relative;
    padding-left: 20px;
    border-left: 2px solid #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}
.timeline-item:before {
    content: '';
    position: absolute;
    top: 8px;
    left: -27px; /* Adjust to align with the border */
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background: #0d6efd;
    border: 2px solid #fff;
    box-shadow: 0 0 5px rgba(13, 110, 253, 0.5);
}
.timeline-year {
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
    padding-left: 1rem;
    position: relative;
}
.timeline-year:before {
    content: '';
    position: absolute;
    left: -29px; /* Adjust for alignment */
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-color: #ffc107;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
}

.timeline-year:first-child {
    margin-top: 0;
}

/* Message board */
.message-form {
    max-width: 600px;
    margin: auto;
}
.emoji-picker-container {
    position: relative;
}
.emoji-list {
    display: none;
    position: absolute;
    border: 1px solid #ddd;
    background: #fff;
    padding: 10px;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 100;
}
.emoji-list span {
    cursor: pointer;
    padding: 5px;
    font-size: 1.2rem;
}
.comment-actions .btn {
    font-size: 0.9rem;
}

/* Custom Blog CSS */
body {
    background-color: #f5f6fa;
}

.blog-header {
    line-height: 1;
    border-bottom: 1px solid #e5e5e5;
}

.blog-header-logo {
    font-family: "Playfair Display", Georgia, "Times New Roman", serif;
    font-size: 2.25rem;
}

.blog-header-logo:hover {
    text-decoration: none;
}

.nav-scroller {
    position: relative;
    z-index: 2;
    height: 2.75rem;
    overflow-y: hidden;
}

.nav-scroller .nav {
    display: flex;
    flex-wrap: nowrap;
    padding-bottom: 1rem;
    margin-top: -1px;
    overflow-x: auto;
    text-align: center;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
}

.main-container {
    padding-top: 1.5rem;
}

.blog-post {
    margin-bottom: 4rem;
}
.blog-post-title {
    margin-bottom: .25rem;
    font-size: 2.5rem;
}
.blog-post-meta {
    margin-bottom: 1.25rem;
    color: #727272;
}

#back-to-top {
    position: fixed;
    bottom: 150px;
    right: 25px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 9999; /* 确保在最上层 */
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

#back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.blog-banner {
    position: relative;
    background: linear-gradient(90deg, #232526 0%, #414345 100%);
    color: #fff;
    box-shadow: 0 4px 24px rgba(80,120,200,0.08);
    margin-bottom: 2rem;
    height: 550px;
    padding-top: 8rem !important;
    overflow: hidden;
}
.blog-banner::before {
    content: '';
    position: absolute;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.4);
    z-index: 1;
    pointer-events: none;
}
.blog-banner > * {
    position: relative;
    z-index: 2;
}
.banner-title {
    font-size: 2.2rem;
    font-weight: 600;
    color: #fff;
    letter-spacing: 2px;
    margin-bottom: 1.2rem;
    font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
}
.banner-line {
    width: 220px;
    height: 1px;
    border-radius: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, #fff 50%, rgba(255,255,255,0) 100%);
    margin: 24px auto 24px auto;
}
.blog-typing {
    color: rgba(255,255,255,0.92);
    font-size: 1.15rem;
    font-family: 'Fira Mono', 'Consolas', monospace;
    min-height: 2.2rem;
}
.blog-typing .text-secondary {
    color: rgba(255,255,255,0.7) !important;
    font-size: 1.05rem;
    font-weight: 400;
}
.typing-cursor {
    color: #fff;
}
.card, .card-header, .card-body {
    border-radius: 1rem !important;
}
.card {
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    background: #fff;
}
.footer {
    background: #f8f9fa;
    border-top: 1px solid #e5e5e5;
}

.navbar.navbar-transparent {
    /* background: rgba(255,255,255,0) !important; */
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    transition: background 0.3s, box-shadow 0.3s;
}
.navbar.scrolled {
    background: #fff !important;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    transition: background 0.3s, box-shadow 0.3s;
}

.navbar.navbar-transparent .navbar-brand,
.navbar.navbar-transparent .nav-link {
    color: #fff !important;
    text-shadow: 0 1px 4px rgba(0,0,0,0.15);
    transition: color 0.3s;
}
.navbar.scrolled .navbar-brand,
.navbar.scrolled .nav-link {
    color: #222 !important;
    text-shadow: none;
    transition: color 0.3s;
}

/* 汉堡按钮颜色切换 */
.navbar.navbar-transparent .navbar-toggler {
    border-color: rgba(255,255,255,0.7);
}
.navbar.navbar-transparent .navbar-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,<svg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'><path stroke='rgba(255,255,255,0.9)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}
.navbar.scrolled .navbar-toggler {
    border-color: #222;
}
.navbar.scrolled .navbar-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,<svg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'><path stroke='rgba(34,34,34,0.9)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

/* 导航栏阴影，去掉 border-bottom */
.navbar {
    box-shadow: 0 2px 16px 0 rgba(0,0,0,0.10);
    border-bottom: none !important;
}


.blogger-avatar {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 1.2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 1rem;
}
.blogger-nickname {
    font-size: 1.3rem;
    font-weight: bold;
    color: #3a4a5a;
    margin-bottom: 0.2rem;
}
.blogger-underline {
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg,rgba(0,198,251,0) 0%,#00c6fb 50%,rgba(0,198,251,0) 100%);
    border-radius: 2px;
    margin-bottom: 0.7rem;
}
.blogger-fulljob, .blogger-sidejob {
    color: #7b8a9a;
    font-size: 0.95rem;
    margin-bottom: 0.1rem;
}
.blogger-signature {
    color: #7b8a9a;
    font-size: 1rem;
    margin-bottom: 1.2rem;
}
.blogger-stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 0.5rem;
}
.blogger-stats a {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3a4a5a;
    min-width: 60px;
    transition: color 0.2s;
    padding: 0.2rem 0;
}
.blogger-stats a:hover {
    color: #005bea;
    text-decoration: none;
}
.blogger-stats .stat-num {
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 2px;
}
.blogger-stats .stat-label {
    font-size: 0.85rem;
    color: #7b8a9a;
}
@media (max-width: 991.98px) {
    aside.col-lg-2 { display: none !important; }
}

/* 扩大主容器宽度 */
.container {
    max-width: 1140px !important;
}

/* 左侧栏宽度自定义为25% */
aside.col-lg-2 {
    flex: 0 0 25%;
    max-width: 25%;
}

.blogger-notice-title {
    font-weight: bold;
    font-size: 1.05rem;
    color: #3a4a5a;
    letter-spacing: 1px;
}
.blogger-notice-content {
    color: #7b8a9a;
    font-size: 0.98rem;
    line-height: 1.7;
}

.blogger-notice-title {
    text-align: left;
}
.blogger-notice-content {
    white-space: pre-line;
}

.archive-list {
    padding: 0 0.5rem;
}
.archive-group {
    margin-bottom: 2.5rem;
}
.archive-group-header {
    font-size: 1.15rem;
    font-weight: bold;
    color: #222;
    display: flex;
    align-items: center;
}
.archive-group-title {
    letter-spacing: 0.5px;
}
.archive-group-count {
    color: #7b8a9a;
    font-size: 1rem;
    font-weight: normal;
    display: flex;
    align-items: center;
}
.archive-article-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
}
.archive-article-list li {
    padding: 0.18rem 0;
    font-size: 1rem;
    color: #222;
    line-height: 1.8;
}
.archive-article-list li a {
    color: #222;
    text-decoration: none;
    transition: color 0.2s;
}
.archive-article-list li a:hover {
    color: #005bea;
    text-decoration: underline;
}
.archive-more {
    color: #7b8a9a;
    font-size: 0.98rem;
}

.archive-group.archive-year {
    background: #f7f9fa;
    border-radius: 1.2rem;
    padding: 1.5rem 1.2rem 1.2rem 1.2rem;
    margin-bottom: 2rem;
}
.archive-article-date {
    color: #b0b8c1;
    font-size: 0.98rem;
    margin-left: 1.2rem;
    white-space: nowrap;
}

main.col-lg-4 {
    flex: 0 0 75%;
    max-width: 75%;
}

/* 个人标签（about页面专用） */
.profile-tag {
    display: inline-block;
    background: #f2f3f5;
    color: #444;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 18px;
    padding: 4px 18px;
    margin: 0 10px;
    margin-bottom: 8px;
    letter-spacing: 1px;
    box-shadow: none;
    border: none;
    transition: background 0.2s, color 0.2s;
}
.profile-tag:not(:last-child) {
    margin-right: 18px;
}
.profile-tag:hover {
    background: #e4e7ed;
    color: #2563eb;
    text-decoration: none;
}

/* 技能标签（云朵感极简） */
.skill-tag {
    display: inline-block;
    background: #f7f8fa;
    color: #555;
    font-size: 0.98rem;
    font-weight: 400;
    border-radius: 20px;
    border: 1px solid #e3e6eb;
    padding: 3px 16px 2px 16px;
    margin: 0 12px 12px 0;
    box-shadow: none;
    letter-spacing: 0.5px;
    transition: color 0.18s, border-color 0.18s;
}
.skill-tag:hover {
    color: #2563eb;
    border-color: #b3c6f7;
    background: #f7f8fa;
    text-decoration: none;
}

/* 联系方式美化 */
.contact-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}
.contact-item {
    display: flex;
    align-items: center;
    min-width: 220px;
    margin-bottom: 10px;
}
.contact-item i {
    font-size: 1.15em;
    color: #2563eb;
    margin-right: 8px;
    opacity: 0.85;
}
.contact-item a {
    color: #2563eb;
    font-weight: 500;
    word-break: break-all;
    text-decoration: none;
    transition: color 0.18s;
}
.contact-item a:hover {
    color: #1e40af;
    text-decoration: underline;
}
@media (max-width: 700px) {
    .contact-row {
        flex-direction: column;
        align-items: flex-start;
    }
    .contact-item {
        min-width: 0;
    }
}

/* 关于本站内容块图标间距 */
.about-feature-icon {
    /* font-size: 1.5em; */
    vertical-align: middle;
    margin-right:5px;
}

/* 联系方式表格式工整布局 */
.contact-table {
    width: 100%;
    max-width: 600px;
    margin: 24px auto 0 auto;
    border-collapse: separate;
    border-spacing: 0 10px;
}
.contact-table td {
    padding: 6px 12px;
    font-size: 1.05rem;
    color: #444;
    background: #f7f8fa;
    border-radius: 8px;
    vertical-align: middle;
    min-width: 120px;
}
.contact-table .contact-label {
    font-weight: 500;
    color: #888;
    text-align: right;
    white-space: nowrap;
    width: 120px;
}
.contact-table .contact-value {
    text-align: left;
    word-break: break-all;
}
@media (max-width: 700px) {
    .contact-table tr {
        display: block;
        margin-bottom: 10px;
    }
    .contact-table td {
        display: block;
        width: 100%;
        text-align: left !important;
        border-radius: 8px !important;
    }
    .contact-table .contact-label {
        text-align: left;
        margin-bottom: 2px;
    }
}

/* 联系方式图标横排极简 */
.contact-icons {
    display: flex;
    align-items: center;
    gap: 28px;
    margin: 24px 0 0 0;
    flex-wrap: wrap;
}
.contact-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: #f7f8fa;
    color: #2563eb;
    font-size: 1.5em;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    box-shadow: none;
    cursor: pointer;
    text-decoration: none;
    border: none;
    position: relative;
}
.contact-icon:hover {
    background: #e4eafe;
    color: #1746a2;
    box-shadow: 0 2px 8px rgba(37,99,235,0.08);
    text-decoration: none;
}
.contact-icon img {
    width: 60%;
    height: 60%;
    object-fit: contain;
    border-radius: 0;
}
@media (max-width: 700px) {
    .contact-icons {
        gap: 18px;
        justify-content: flex-start;
    }
    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }
}

/* 打赏按钮区 */
.reward-section {
    text-align: center;
    margin: 36px 0 0 0;
}
.reward-tip {
    color: #888;
    font-size: 1.15em;
    margin-bottom: 18px;
}
.reward-btns {
    display: flex;
    justify-content: center;
    gap: 28px;
    margin-top: 10px;
}
.reward-btn {
    display: inline-flex;
    align-items: center;
    font-size: 1.18em;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    padding: 0.55em 1.5em;
    cursor: pointer;
    color: #fff;
    transition: box-shadow 0.18s, background 0.18s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    outline: none;
}
.reward-btn-alipay {
    background: #00aaff;
}
.reward-btn-alipay:hover {
    background: #008fd6;
}
.reward-btn-wechat {
    background: #22c35a;
}
.reward-btn-wechat:hover {
    background: #179b44;
}
.reward-btn i {
    font-size: 1.2em;
    margin-right: 8px;
}
/* 打赏二维码弹窗 */
.reward-qr-mask {
    position: fixed;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.35);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}
.reward-qr-popup {
    background: #fff;
    border-radius: 14px;
    padding: 24px 24px 12px 24px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.10);
    text-align: center;
    max-width: 280px;
}
.reward-qr-popup img {
    max-width: 200px;
    margin: 0 auto 12px auto;
    display: block;
}
.reward-qr-title {
    color: #2563eb;
    font-size: 1.08em;
    margin-bottom: 8px;
}
.reward-qr-close {
    color: #2563eb;
    cursor: pointer;
    margin-top: 10px;
    font-size: 1.05em;
}

/* 打赏二维码直接显示样式 */
.reward-qr-item {
    padding: 30px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    margin-bottom: 20px;
}
.reward-qr-item:hover {
    background: #fff;
    border-color: #2563eb;
    box-shadow: 0 2px 12px rgba(37,99,235,0.1);
}
.reward-qr-item h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.1em;
}
.reward-qr-item .reward-qr-img {
    max-width: 220px !important;
    width: 220px !important;
    height: 220px !important;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    background: #fff;
    padding: 8px;
    object-fit: contain;
}
@media (max-width: 768px) {
    .reward-qr-item {
        padding: 20px 15px;
        margin-bottom: 15px;
    }
    .reward-qr-item .reward-qr-img {
        max-width: 180px !important;
        width: 180px !important;
        height: 180px !important;
    }
    .reward-qr-item h6 {
        margin-bottom: 15px;
        font-size: 1em;
    }
}

/* 友链页面样式 */
.friend-link-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}
.friend-link-card:hover {
    border-color: #2563eb;
    box-shadow: 0 4px 16px rgba(37,99,235,0.12);
    transform: translateY(-2px);
}
.friend-link-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.friend-link-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.4em;
    margin-right: 16px;
    flex-shrink: 0;
}
.friend-link-card:nth-child(1) .friend-link-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.friend-link-card:nth-child(2) .friend-link-avatar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.friend-link-card:nth-child(3) .friend-link-avatar {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.friend-link-card:nth-child(4) .friend-link-avatar {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.friend-link-info {
    flex: 1;
}
.friend-link-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    line-height: 1.3;
}
.friend-link-desc {
    color: #718096;
    font-size: 0.9em;
    line-height: 1.5;
    margin: 0;
}
.friend-link-footer {
    border-top: 1px solid #f1f5f9;
    padding-top: 16px;
    text-align: center;
}
.friend-link-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: #f8fafc;
    color: #64748b;
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.9em;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
}
.friend-link-btn:hover {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(37,99,235,0.2);
}
@media (max-width: 768px) {
    .friend-link-card {
        padding: 16px;
    }
    .friend-link-avatar {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
        margin-right: 12px;
    }
    .friend-link-title {
        font-size: 1em;
    }
    .friend-link-desc {
        font-size: 0.85em;
    }
}

/* 友链logo图片样式 */
.friend-link-logo {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    object-fit: cover;
    margin-right: 16px;
    background: #f3f4f6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    border: 1px solid #e5e7eb;
}
@media (max-width: 768px) {
    .friend-link-logo {
        width: 38px;
        height: 38px;
        margin-right: 10px;
    }
}

/* 申请友链表单美化 */
form .form-label {
    font-weight: 500;
    /* color: #2563eb; */
}
form .form-control {
    border-radius: 8px;
    border: 1px solid #e3e6eb;
    box-shadow: none;
    transition: border-color 0.2s;
}
form .form-control:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37,99,235,0.08);
}
form button[type="submit"] {
    border-radius: 8px;
    padding: 8px 32px;
    font-weight: 600;
} 
@media (max-width: 991.98px) {
    /* 主内容区和侧栏上下分布时，增加间距 */
    .fadfagihag {
      margin-top: 24px !important;
    border-color: #2563eb;
    }
  }

  #global-loading {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000 !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: opacity 0.4s;
  }

.custom-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 18px;
}

.spinner-core {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #3498db, #8e44ad, #f093fb, #3498db);
  animation: spin-core 1.2s linear infinite;
  box-shadow: 0 4px 24px rgba(80,120,200,0.10);
  margin-bottom: 12px;
  position: relative;
}
.spinner-core::after {
  content: '';
  position: absolute;
  left: 50%; top: 50%;
  transform: translate(-50%, -50%);
  width: 32px; height: 32px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(80,120,200,0.06);
}

@keyframes spin-core {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.spinner-wave {
  display: flex;
  gap: 6px;
  margin-top: 2px;
}
.spinner-wave .dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
  opacity: 0.7;
  animation: wave-bounce 1.2s infinite;
}
.spinner-wave .dot:nth-child(1) { animation-delay: 0s; }
.spinner-wave .dot:nth-child(2) { animation-delay: 0.15s; }
.spinner-wave .dot:nth-child(3) { animation-delay: 0.3s; }
.spinner-wave .dot:nth-child(4) { animation-delay: 0.45s; }
.spinner-wave .dot:nth-child(5) { animation-delay: 0.6s; }

@keyframes wave-bounce {
  0%, 100% { transform: translateY(0); opacity: 0.7;}
  50% { transform: translateY(-16px); opacity: 1;}
}

.loading-text {
  color: #5a5a7a;
  font-size: 17px;
  letter-spacing: 2px;
  font-weight: 500;
  text-shadow: 0 2px 8px rgba(80,120,200,0.06);
  margin-top: 8px;
}

#star-bg-canvas {
  position: fixed;
  left: 0; top: 0; width: 100vw; height: 100vh;
  z-index: -1;
  pointer-events: none;
  background: #101020;
}
body, html {
}
/* #global-loading, .container, .main-content, .blog-banner, .banner-section, .card, .footer, header, aside, main {
  position: relative;
  z-index: 1;
} */

.star-context-menu {
  position: fixed;
  z-index: 10001;
  min-width: 210px;
  background: rgba(24, 26, 38, 0.96);
  border-radius: 14px;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25), 0 0 24px 2px #3a3a5a44;
  border: 1.5px solid rgba(255,255,255,0.08);
  backdrop-filter: blur(8px);
  color: #e0e0f0;
  font-size: 16px;
  padding: 8px 0;
  user-select: none;
  transition: opacity 0.18s;
}
.star-context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.star-context-menu li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 22px;
  cursor: pointer;
  border-bottom: 1px solid rgba(255,255,255,0.06);
  transition: background 0.15s, color 0.15s;
}
.star-context-menu li:last-child {
  border-bottom: none;
}
.star-context-menu li:hover {
  background: rgba(80, 120, 200, 0.13);
  color: #fff;
}
.star-context-menu i {
  width: 22px;
  text-align: center;
  font-size: 1.1em;
  opacity: 0.85;
}

.star-ring-menu {
  position: fixed;
  z-index: 10001;
  pointer-events: none;
  left: 0; top: 0;
  width: 0; height: 0;
  transition: opacity 0.25s;
}
.star-ring-center {
  position: absolute;
  left: 50%; top: 50%;
  width: 38px; height: 38px;
  background: radial-gradient(circle, #fff8, #3a3a5a88 80%);
  border-radius: 50%;
  box-shadow: 0 0 32px 8px #7ecfff55, 0 0 0 2px #fff2;
  transform: translate(-50%, -50%);
  pointer-events: none;
}
.star-ring-item {
  position: absolute;
  left: 50%; top: 50%;
  width: 120px; height: 38px;
  background: rgba(24,26,38,0.92);
  border-radius: 22px;
  box-shadow: 0 2px 16px 0 #3a3a5a44;
  color: #e0e0f0;
  display: flex; align-items: center; gap: 10px;
  justify-content: center;
  font-size: 16px;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.7) rotate(0deg);
  transition: 
    opacity 0.25s cubic-bezier(.4,2,.6,1),
    transform 0.35s cubic-bezier(.4,2,.6,1);
  pointer-events: auto;
  cursor: pointer;
  border: 1.5px solid rgba(255,255,255,0.08);
  backdrop-filter: blur(6px);
}
.star-ring-item i {
  font-size: 1.2em;
  color: #7ecfff;
  filter: drop-shadow(0 0 6px #7ecfff88);
}
.star-ring-item span {
  font-weight: 500;
  letter-spacing: 1px;
}
.star-ring-item:hover {
  background: rgba(80,120,200,0.18);
  color: #fff;
  box-shadow: 0 0 16px 2px #7ecfff55;
  transform: translate(-50%, -50%) scale(1.08) rotate(var(--angle, 0deg));
}
.star-ring-item-inner {
  display: flex;
  align-items: center;
  gap: 10px;
  transition: transform 0.35s cubic-bezier(.4,2,.6,1);
}