/* 自定义鼠标图标 - 使用!important确保优先级 */
body, html, * {
  cursor: url('/uploads/pointer.cur'), auto !important;
}

/* 链接元素使用特殊鼠标图标 */
a, a:hover, a:focus, a:active,
button, button:hover, button:focus, button:active,
.btn, .btn:hover, .btn:focus, .btn:active,
[role="button"], [role="button"]:hover,
input[type="button"], input[type="submit"], input[type="reset"],
.clickable, .pointer {
  cursor: url('/uploads/link.cur'), pointer !important;
}

/* 禁用状态保持默认样式 */
[disabled], .disabled,
input[disabled], button[disabled],
.btn[disabled], .btn.disabled {
  cursor: not-allowed !important;
}

/* 文本输入框保持文本光标 */
input[type="text"], input[type="password"], input[type="email"],
input[type="search"], input[type="tel"], input[type="url"],
input[type="number"], textarea, [contenteditable="true"] {
  cursor: text !important;
}

.falling-flower {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  user-select: none;
  will-change: transform, top, left;
}

/* 移动时的简洁波纹效果 */
.cursor-move-effect {
  position: fixed;
  pointer-events: none;
  z-index: 99998;
  left: 0; top: 0;
  transform: translate(-50%, -50%);
  width: 0; height: 0;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: move-ripple-anim 0.6s ease-out;
}

@keyframes move-ripple-anim {
  0% { 
    width: 0; height: 0; 
    opacity: 0.8; 
    border-width: 2px;
  }
  100% { 
    width: 80px; height: 80px; 
    opacity: 0; 
    border-width: 1px;
  }
}

/* 点击时的规则火花扩散特效 */
.arc-cursor-effect {
  position: absolute;
  width: 2px; height: 16px;
  background: #fff;
  border-radius: 3px;
  transform: rotate(var(--arc-rotate, 0deg)) translateY(-4px) scaleY(1);
  transform-origin: center top;
  opacity: 1;
  
  animation: arc-cursor-anim 0.5s cubic-bezier(0.4,0,0.2,1);
  pointer-events: none;
}

@keyframes arc-cursor-anim {
  0% {
    transform: rotate(var(--arc-rotate, 0deg)) translateY(-4px) scaleY(1);
    opacity: 1;
  }
  100% {
    transform: rotate(var(--arc-rotate, 0deg)) translateY(-4px) scaleY(0);
    opacity: 0;
  }
}

.arc-burst-group {
  position: fixed;
  left: 0; top: 0;
  pointer-events: none;
  width: 100px; height: 100px;

  z-index: 99999;
  margin-left: -50px; margin-top: -50px;

}

.glass-cursor-spot {
  position: fixed;
  left: 0; top: 0;
  width: 0; height: 0;
  pointer-events: none;
  border-radius: 50%;
  background: rgba(255,255,255,0.25);
  box-shadow: 0 0 32px 8px rgba(255,255,255,0.25), 0 0 64px 16px rgba(255,255,255,0.12);
  backdrop-filter: blur(6px);
  transform: translate(-50%, -50%) scale(0.5);
  opacity: 1;
  transition:
    width 0.6s cubic-bezier(0.4,0,0.2,1),
    height 0.6s cubic-bezier(0.4,0,0.2,1),
    opacity 0.6s cubic-bezier(0.4,0,0.2,1),
    box-shadow 0.6s cubic-bezier(0.4,0,0.2,1),
    background 0.6s cubic-bezier(0.4,0,0.2,1),
    filter 0.6s cubic-bezier(0.4,0,0.2,1);
  z-index: 99999;
  will-change: width, height, opacity, box-shadow, background, filter;
}
.glass-cursor-spot.show {
  width: 80px;
  height: 80px;
  opacity: 0;
  box-shadow: 0 0 64px 32px rgba(255,255,255,0.12), 0 0 128px 32px rgba(255,255,255,0.08);
  background: rgba(255,255,255,0.18);
  filter: blur(2px);
}

.custom-context-menu {
  width: 200px;
  min-width: 0;
  position: fixed;
  z-index: 10001;
  background: rgba(24, 26, 38, 0.96);
  border-radius: 14px;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25), 0 0 24px 2px #3a3a5a44;
  border: 1.5px solid rgba(255,255,255,0.08);
  color: #e0e0f0;
  font-size: 16px;
  padding: 8px 0;
  user-select: none;
  transition: opacity 0.18s;
}
.custom-context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.custom-context-menu li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 22px;
  cursor: pointer;
  border-bottom: 1px solid rgba(255,255,255,0.06);
  transition: background 0.15s, color 0.15s;
  background: none;
}
.custom-context-menu li:last-child,
.custom-context-menu .divider {
  border-bottom: none;
}
.custom-context-menu li:hover {
  background: rgba(80, 120, 200, 0.13);
  color: #fff;
}
.custom-context-menu i {
  width: 22px;
  text-align: center;
  font-size: 1.1em;
  opacity: 0.85;
}
.custom-context-menu .divider {
  height: 1px;
  background: rgba(255,255,255,0.08);
  margin: 4px 0;
  pointer-events: none;
}

/* 右键菜单前四个图标横排大图标样式 */
.context-menu-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 4px;
  border-bottom: 1px solid rgba(255,255,255,0.08);
  width: 100%;
}
.custom-context-menu .icon-btn {
  flex: 1 1 0;
  height: 36px;
  margin: 0 2px;
  border-radius: 10px;
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: flex 0.22s cubic-bezier(.4,1.4,.6,1), background 0.18s;
}
.context-menu-row:hover .icon-btn {
  flex-basis: 24px;
}
.context-menu-row .icon-btn:hover, .context-menu-row .icon-btn:focus {
  background: rgba(80, 120, 200, 0.13);
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(255,255,255,0.06);
  text-decoration: none;
}
.custom-context-menu .icon-btn i {
  font-size: 1.1em;
  color: #e0e0f0;
  opacity: 1;
  transition: color 0.18s;
}
.custom-context-menu .icon-btn:hover, .custom-context-menu .icon-btn.active {
  background: rgba(80, 120, 200, 0.13);
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(255,255,255,0.06);
  text-decoration: none;
}
.custom-context-menu .icon-btn:hover i, .custom-context-menu .icon-btn.active i {
  color: #fff;
}

/* --- Selective Squeeze Logic --- */

/* Hover Icon 1 */
.context-menu-row:has(.icon-btn:nth-child(1):hover) .icon-btn:nth-child(1) { flex: 1.25; }
.context-menu-row:has(.icon-btn:nth-child(1):hover) .icon-btn:nth-child(2) { flex: 0.9; }
.context-menu-row:has(.icon-btn:nth-child(1):hover) .icon-btn:nth-child(3) { flex: 0.9; }
.context-menu-row:has(.icon-btn:nth-child(1):hover) .icon-btn:nth-child(4) { flex: 1; }


/* Hover Icon 2 */
.context-menu-row:has(.icon-btn:nth-child(2):hover) .icon-btn:nth-child(2) { flex: 1.25; }
.context-menu-row:has(.icon-btn:nth-child(2):hover) .icon-btn:nth-child(3) { flex: 0.75; }
.context-menu-row:has(.icon-btn:nth-child(2):hover) .icon-btn:nth-child(4) { flex: 1; }
.context-menu-row:has(.icon-btn:nth-child(2):hover) .icon-btn:nth-child(1) { flex: 1; }


/* Hover Icon 3 */
.context-menu-row:has(.icon-btn:nth-child(3):hover) .icon-btn:nth-child(3) { flex: 1.25; }
.context-menu-row:has(.icon-btn:nth-child(3):hover) .icon-btn:nth-child(2) { flex: 0.75; }
.context-menu-row:has(.icon-btn:nth-child(3):hover) .icon-btn:nth-child(1) { flex: 1; }
.context-menu-row:has(.icon-btn:nth-child(3):hover) .icon-btn:nth-child(4) { flex: 1; }

/* Hover Icon 4 */
.context-menu-row:has(.icon-btn:nth-child(4):hover) .icon-btn:nth-child(4) { flex: 1.25; }
.context-menu-row:has(.icon-btn:nth-child(4):hover) .icon-btn:nth-child(2) { flex: 0.9; }
.context-menu-row:has(.icon-btn:nth-child(4):hover) .icon-btn:nth-child(3) { flex: 0.9; }
.context-menu-row:has(.icon-btn:nth-child(4):hover) .icon-btn:nth-child(1) { flex: 1; }


/* General hover style */
.custom-context-menu .icon-btn:hover {
  background: rgba(80, 120, 200, 0.13);
  color: #fff;
  box-shadow: 0 2px 8px rgba(255,255,255,0.06);
}
