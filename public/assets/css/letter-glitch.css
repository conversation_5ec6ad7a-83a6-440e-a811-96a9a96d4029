/* Letter Glitch Effect */
.letter-glitch {
  position: relative;
  display: inline-block;
  font-weight: bold;
  text-transform: uppercase;
  color: #fff;
  text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
               0.025em 0.04em 0 #fffc00;
  animation: glitch 725ms infinite;
}

.letter-glitch span {
  position: absolute;
  top: 0;
  left: 0;
}

.letter-glitch span:first-child {
  animation: glitch 500ms infinite;
  clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%);
  transform: translate(-0.04em, -0.03em);
  opacity: 0.75;
}

.letter-glitch span:last-child {
  animation: glitch 375ms infinite;
  clip-path: polygon(0 65%, 100% 65%, 100% 100%, 0 100%);
  transform: translate(0.04em, 0.03em);
  opacity: 0.75;
}

@keyframes glitch {
  0% {
    text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
                 0.025em 0.04em 0 #fffc00;
  }
  15% {
    text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
                 0.025em 0.04em 0 #fffc00;
  }
  16% {
    text-shadow: -0.05em -0.025em 0 #00fffc, 0.025em 0.035em 0 #fc00ff,
                 -0.05em -0.05em 0 #fffc00;
  }
  49% {
    text-shadow: -0.05em -0.025em 0 #00fffc, 0.025em 0.035em 0 #fc00ff,
                 -0.05em -0.05em 0 #fffc00;
  }
  50% {
    text-shadow: 0.05em 0.035em 0 #00fffc, 0.03em 0 0 #fc00ff,
                 0 -0.04em 0 #fffc00;
  }
  99% {
    text-shadow: 0.05em 0.035em 0 #00fffc, 0.03em 0 0 #fc00ff,
                 0 -0.04em 0 #fffc00;
  }
  100% {
    text-shadow: -0.05em 0 0 #00fffc, -0.025em -0.04em 0 #fc00ff,
                 -0.04em -0.025em 0 #fffc00;
  }
}

/* 更强烈的故障效果 */
.letter-glitch-intense {
  position: relative;
  display: inline-block;
  font-weight: bold;
  text-transform: uppercase;
  color: #fff;
  text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff,
               0.025em 0.04em 0 #fffc00;
  animation: glitch-intense 1s infinite;
}

.letter-glitch-intense::before,
.letter-glitch-intense::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.letter-glitch-intense::before {
  animation: glitch-intense-1 0.5s infinite;
  color: #00fffc;
  z-index: -1;
}

.letter-glitch-intense::after {
  animation: glitch-intense-2 0.5s infinite;
  color: #fc00ff;
  z-index: -2;
}

@keyframes glitch-intense {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

@keyframes glitch-intense-1 {
  0% {
    clip-path: inset(40% 0 61% 0);
    transform: skew(0.15deg);
  }
  20% {
    clip-path: inset(92% 0 1% 0);
    transform: skew(0.25deg);
  }
  40% {
    clip-path: inset(43% 0 1% 0);
    transform: skew(0.1deg);
  }
  60% {
    clip-path: inset(25% 0 58% 0);
    transform: skew(-0.15deg);
  }
  80% {
    clip-path: inset(54% 0 7% 0);
    transform: skew(-0.25deg);
  }
  100% {
    clip-path: inset(58% 0 43% 0);
    transform: skew(0.15deg);
  }
}

@keyframes glitch-intense-2 {
  0% {
    clip-path: inset(65% 0 1% 0);
    transform: skew(0.1deg);
  }
  20% {
    clip-path: inset(10% 0 85% 0);
    transform: skew(0.15deg);
  }
  40% {
    clip-path: inset(75% 0 14% 0);
    transform: skew(-0.1deg);
  }
  60% {
    clip-path: inset(5% 0 90% 0);
    transform: skew(-0.15deg);
  }
  80% {
    clip-path: inset(80% 0 5% 0);
    transform: skew(0.1deg);
  }
  100% {
    clip-path: inset(15% 0 80% 0);
    transform: skew(0.15deg);
  }
}

/* 悬停效果 */
.letter-glitch:hover {
  animation: glitch 200ms infinite;
}

.letter-glitch-intense:hover {
  animation: glitch-intense 300ms infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .letter-glitch,
  .letter-glitch-intense {
    font-size: 0.9em;
  }
}

@media (max-width: 480px) {
  .letter-glitch,
  .letter-glitch-intense {
    font-size: 0.8em;
  }
} 