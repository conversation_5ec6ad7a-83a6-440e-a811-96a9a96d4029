window.addEventListener('DOMContentLoaded', function() {
  const flowerUrl = '/uploads/luohua.png';
  const maxFlowers = 24;
  const body = document.body;
  let docWidth = window.innerWidth;
  let docHeight = window.innerHeight;

  function randomBetween(a, b) {
    return a + Math.random() * (b - a);
  }

  function createFlower(initial) {
    const img = document.createElement('img');
    img.src = flowerUrl;
    img.className = 'falling-flower';
    img.style.position = 'fixed';
    img.style.zIndex = 9999;
    img.style.pointerEvents = 'none';
    const size = randomBetween(18, 48);
    img.style.width = size + 'px';
    img.style.height = 'auto';
    img.style.opacity = randomBetween(0.5, 0.95);

    // 轨迹参数：右上到左下
    let x = randomBetween(docWidth * 0.6, docWidth + 60);
    let y = initial
      ? randomBetween(-docHeight, docHeight * 0.8) // 初始时让花瓣分布在整个y轴
      : randomBetween(-60, -20); // 后续新花瓣从顶部右侧生成

    img.style.left = x + 'px';
    img.style.top = y + 'px';
    img.style.transition = 'transform 0.2s';
    body.appendChild(img);

    // 动画参数
    const speedY = randomBetween(0.5, 1.2) * (size / 32);
    const speedX = -randomBetween(0.7, 2.2) * (size / 32); // 向左飘
    const swing = randomBetween(10, 40) * (size / 32);
    const swingSpeed = randomBetween(1.2, 2.8);
    let angle = randomBetween(0, Math.PI * 2);
    const rotateBase = randomBetween(-180, 180);
    const rotateSpeed = randomBetween(-0.5, 0.5);
    const scaleBase = randomBetween(0.95, 1.05);
    const scaleSpeed = randomBetween(-0.003, 0.003);

    function animate() {
      y += speedY;
      x += speedX;
      angle += 0.01 * swingSpeed;
      img.style.top = y + 'px';
      img.style.left = (x + Math.sin(angle) * swing) + 'px';
      img.style.transform =
        `rotate(${rotateBase + Math.sin(angle) * 30 + (y * rotateSpeed)}deg) scale(${scaleBase + Math.sin(angle) * scaleSpeed * 100})`;

      // 超出屏幕则移除花瓣
      if (y > docHeight + 40 || x < -60) {
        img.remove();
        return;
      }
      requestAnimationFrame(animate);
    }
    animate();
  }

  // 适应窗口变化
  window.addEventListener('resize', function() {
    docWidth = window.innerWidth;
    docHeight = window.innerHeight;
  });

  // 初始时生成 maxFlowers 朵，分布在不同y轴
  for (let i = 0; i < maxFlowers; i++) {
    createFlower(true);
  }

  // 持续生成新花瓣
  setInterval(function() {
    // 页面上花瓣数量不超过 maxFlowers
    if (document.querySelectorAll('.falling-flower').length < maxFlowers) {
      createFlower(false);
    }
  }, 800); // 每0.8秒尝试生成一朵

}); 