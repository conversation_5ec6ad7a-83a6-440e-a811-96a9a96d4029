define(['jquery', 'template', 'moment'], function ($, Template, Moment) {
    var Comment = {
        init: function (options) {
            options = options || {};
            var container = options.container || '.comment-section';
            var self = this;

            this.container = $(container);
            if (this.container.length === 0) return;

            this.type = this.container.data('type');
            this.aid = this.container.data('id');
            this.page = 1;
            this.sort = 'new';
            this.event_prefix = 'comment.' + this.type + '.' + this.aid;

            // 渲染评论列表
            this.renderList();
            
            // 绑定事件
            this.bindEvents();
        },

        renderList: function () {
            var self = this;
            var commentList = self.container.find('.comment-list');

            // 每次渲染前，先显示加载状态
            commentList.html('<div class="text-center text-muted py-5">正在加载留言...</div>');

            $.get('/api/comment/list', {
                type: this.type,
                aid: this.aid,
                page: this.page,
                sort: this.sort
            }).then(function (res) {
                if (res.code === 1) {
                    if (res.data.list && res.data.list.length > 0) {
                        var html = self.renderTpl(res.data.list);
                        commentList.html(html);
                        var paginator = self.renderPaginator(res.data.total);
                        commentList.append(paginator);
                    } else {
                        commentList.html('<div class="text-center text-muted py-5">暂无留言，快来抢沙发吧！</div>');
                    }
                } else {
                    var errorMsg = res.msg || '加载失败，请稍后重试。';
                    commentList.html('<div class="text-center text-danger py-5">' + errorMsg + ' <a href="#" class="btn-retry-load">点击重试</a></div>');
                }
            }).fail(function() {
                commentList.html('<div class="text-center text-danger py-5">加载评论失败，请检查网络连接。 <a href="#" class="btn-retry-load">点击重试</a></div>');
            });
        },

        bindEvents: function () {
            var self = this;
            
            // 表单提交
            self.container.closest('.card-body').find('.comment-form').on('submit', function(e){
                e.preventDefault();
                var form = $(this);
                var data = form.serializeArray().reduce(function(obj, item) {
                    obj[item.name] = item.value;
                    return obj;
                }, {});
                // 自动识别浏览器和设备
                data.browser = (function(){
                    var ua = navigator.userAgent;
                    if (ua.indexOf('Chrome') > -1) return 'Chrome';
                    if (ua.indexOf('Firefox') > -1) return 'Firefox';
                    if (ua.indexOf('Safari') > -1) return 'Safari';
                    if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident') > -1) return 'IE';
                    return 'Other';
                })();
                data.device = (function(){
                    var ua = navigator.userAgent;
                    if (/mobile/i.test(ua)) return 'Mobile';
                    if (/iPad|Tablet/i.test(ua)) return 'Tablet';
                    return 'PC';
                })();
                data.type = self.type;
                data.article_id = self.aid;
                $.post('/api/comment/post', data).then(function(res){
                    if (res.code === 1) {
                        alert('发表成功！');
                        form[0].reset();
                        self.renderList(); // 重新加载列表
                    } else {
                        alert(res.msg);
                    }
                });
            });

            // 分页点击
            self.container.on('click', '.paginator a', function(e){
                e.preventDefault();
                self.page = $(this).data('page');
                self.renderList();
            });

            // 点赞
            self.container.on('click', '.btn-like', function(e){
                e.preventDefault();
                var btn = $(this);
                var id = btn.data('id');
                var likedComments = JSON.parse(localStorage.getItem('likedComments') || '[]');
                var isLiked = likedComments.includes(id);
                $.post('/api/comment/like', {comment_id: id}).then(function(res){
                    if(res.code === 1) {
                        var countSpan = btn.find('span');
                        var count = parseInt(countSpan.text());
                        if(isLiked) {
                            btn.removeClass('liked');
                            countSpan.text(count - 1);
                            likedComments = likedComments.filter(function(cid){ return cid !== id; });
                        } else {
                            btn.addClass('liked');
                            countSpan.text(count + 1);
                            likedComments.push(id);
                        }
                        localStorage.setItem('likedComments', JSON.stringify(likedComments));
                    }
                });
            });

            // 重试加载
            self.container.on('click', '.btn-retry-load', function(e){
                e.preventDefault();
                self.renderList();
            });

            // 自定义下拉框
            self.container.on('click', '.custom-select-trigger', function(){
                $(this).closest('.custom-select').toggleClass('open');
            });
            self.container.on('click', '.custom-option', function(){
                var option = $(this);
                var select = option.closest('.custom-select');
                var trigger = select.find('.custom-select-trigger');
                
                trigger.find('span').text(option.text());
                select.removeClass('open');
                
                self.sort = option.data('value');
                self.page = 1;
                self.renderList();
            });
            
            // 点击外部关闭下拉框
            $(document).on('click', function(e){
                if (!$(e.target).closest('.custom-select').length) {
                    $('.custom-select').removeClass('open');
                }
            });
        },
        
        renderTpl: function(list){
            var likedComments = JSON.parse(localStorage.getItem('likedComments') || '[]');
            var html = '';
            list.forEach(function(item){
                var likedClass = likedComments.includes(item.id) ? 'liked' : '';
                html += `
                <div class="comment-item">
                    <img src="${item.avatar}" class="comment-avatar" alt="${item.nickname}">
                    <div class="comment-body">
                        <div class="comment-header">
                            <span class="comment-nickname">${item.nickname}</span>
                            <span class="comment-time">${Moment(item.createtime * 1000).fromNow()}</span>
                        </div>
                        <div class="comment-content">${item.content}</div>
                        <div class="comment-actions">
                            <a href="#" class="btn-like ${likedClass}" data-id="${item.id}"><i class="fa fa-thumbs-up"></i> <span>${item.likes || 0}</span></a>
                        </div>
                    </div>
                </div>`;
            });
            return html;
        },

        renderPaginator: function (total) {
            var limit = 10;
            var pages = Math.ceil(total / limit);
            if (pages <= 1) return '';

            var html = '<nav class="paginator"><ul class="pagination">';
            for (var i = 1; i <= pages; i++) {
                html += `<li class="page-item ${i === this.page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
            }
            html += '</ul></nav>';
            return html;
        }
    };

    return Comment;
}); 