(function() {
  let lastMoveTime = 0;
  const moveThrottle = 300; // 移动触发的最小间隔时间（毫秒）

  // 移动时的简洁波纹效果
  function createMoveEffect(x, y) {
    const ripple = document.createElement('div');
    ripple.className = 'cursor-move-effect';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    document.body.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  // 点击时的玻璃高光/光斑特效
  function createGlassSpot(x, y) {
    const spot = document.createElement('div');
    spot.className = 'glass-cursor-spot';
    spot.style.left = x + 'px';
    spot.style.top = y + 'px';
    document.body.appendChild(spot);
    // 触发动画
    requestAnimationFrame(() => {
      spot.classList.add('show');
    });
    setTimeout(() => spot.remove(), 600);
  }

  document.addEventListener('pointerdown', function(e) {
    if (e.button === 0 || e.pointerType === 'touch') {
      createGlassSpot(e.clientX, e.clientY);
    }
  });

  document.addEventListener('mousemove', function(e) {
    const now = Date.now();
    if (now - lastMoveTime > moveThrottle) {
      createMoveEffect(e.clientX, e.clientY);
      lastMoveTime = now;
    }
  });
})(); 