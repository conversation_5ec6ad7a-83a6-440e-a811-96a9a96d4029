// Letter Glitch 效果增强脚本
(function() {
    'use strict';
    
    // 为所有 letter-glitch 元素添加随机故障效果
    function addRandomGlitch() {
        const glitchElements = document.querySelectorAll('.letter-glitch, .letter-glitch-intense');
        
        glitchElements.forEach(element => {
            // 随机触发故障效果
            setInterval(() => {
                if (Math.random() < 0.1) { // 10% 概率触发
                    element.style.animationDuration = '200ms';
                    setTimeout(() => {
                        element.style.animationDuration = '';
                    }, 200);
                }
            }, 2000);
            
            // 鼠标悬停时增强效果
            element.addEventListener('mouseenter', function() {
                this.style.animationDuration = '100ms';
            });
            
            element.addEventListener('mouseleave', function() {
                this.style.animationDuration = '';
            });
        });
    }
    
    // 创建动态故障文字
    function createDynamicGlitch(text, container) {
        const glitchText = document.createElement('div');
        glitchText.className = 'letter-glitch';
        glitchText.setAttribute('data-text', text);
        glitchText.innerHTML = `
            ${text}
            <span>${text}</span>
            <span>${text}</span>
        `;
        
        if (container) {
            container.appendChild(glitchText);
        }
        
        return glitchText;
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        addRandomGlitch();
    });
    
    // 导出函数供外部使用
    window.LetterGlitch = {
        create: createDynamicGlitch,
        addRandomGlitch: addRandomGlitch
    };
})(); 