/**
 * 博客系统JavaScript功能
 */

$(document).ready(function() {
    // 初始化表情选择器
    initEmojiPicker();
    
    // 初始化点赞功能
    initLikeSystem();
    


    // 初始化并定时更新运行时间
    calculateRunTime();
    setInterval(calculateRunTime, 60000);

    // "回到顶部"按钮的显示/隐藏逻辑
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('#backToTop').fadeIn();
        } else {
            $('#backToTop').fadeOut();
        }
    });
    
    // "回到顶部"按钮的点击事件
    $('#backToTop').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    // 导航栏透明与滚动切换
    // function toggleNavbarBg() {
    //     if ($(window).scrollTop() < 40) {
    //         $('.navbar').addClass('navbar-transparent').removeClass('scrolled');
    //     } else {
    //         $('.navbar').removeClass('navbar-transparent').addClass('scrolled');
    //     }
    // }
    // toggleNavbarBg();
    // $(window).on('scroll', toggleNavbarBg);
});

/**
 * 初始化表情选择器
 */
function initEmojiPicker() {
    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠'];
    
    // 创建表情选择器
    const emojiPicker = $('<div class="emoji-picker"></div>');
    emojis.forEach(function(emoji) {
        emojiPicker.append(`<span class="emoji-item">${emoji}</span>`);
    });
    
    // 表情按钮点击
    $('.emoji-btn').on('click', function(e) {
        e.preventDefault();
        const textarea = $(this).siblings('textarea');
        const picker = emojiPicker.clone();
        
        // 定位表情选择器
        const offset = $(this).offset();
        picker.css({
            top: offset.top + 30,
            left: offset.left
        });
        
        // 显示表情选择器
        $('body').append(picker);
        picker.show();
        
        // 表情点击
        picker.on('click', '.emoji-item', function() {
            const emoji = $(this).text();
            const cursorPos = textarea[0].selectionStart;
            const text = textarea.val();
            const textBefore = text.substring(0, cursorPos);
            const textAfter = text.substring(cursorPos);
            
            textarea.val(textBefore + emoji + textAfter);
            textarea[0].setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
            textarea.focus();
            
            picker.remove();
        });
        
        // 点击其他地方关闭表情选择器
        $(document).one('click', function() {
            picker.remove();
        });
    });
}

/**
 * 初始化点赞功能
 */
function initLikeSystem() {
    $(document).on('click', '.comment-like', function() {
        const commentId = $(this).data('comment-id');
        const likeElement = $(this);
        
        $.ajax({
            url: '/comment/like',
            type: 'POST',
            data: { comment_id: commentId },
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    // 更新点赞状态
                    const icon = likeElement.find('i');
                    const count = likeElement.find('.like-count');
                    
                    if (response.msg.includes('取消')) {
                        icon.removeClass('text-danger');
                        count.text(parseInt(count.text()) - 1);
                    } else {
                        icon.addClass('text-danger');
                        count.text(parseInt(count.text()) + 1);
                    }
                    
                    showMessage(response.msg, 'success');
                } else {
                    showMessage(response.msg || '操作失败！', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后重试！', 'error');
            }
        });
    });
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    // 添加到页面顶部
    $('.main-content').prepend(alert);
    
    // 3秒后自动消失
    setTimeout(function() {
        alert.alert('close');
    }, 3000);
}

/**
 * 格式化时间
 */
function formatTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp * 1000);
    const diff = now - time;
    
    const minute = 60 * 1000;
    const hour = minute * 60;
    const day = hour * 24;
    const month = day * 30;
    const year = day * 365;
    
    if (diff < minute) {
        return '刚刚';
    } else if (diff < hour) {
        return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
        return Math.floor(diff / hour) + '小时前';
    } else if (diff < month) {
        return Math.floor(diff / day) + '天前';
    } else if (diff < year) {
        return Math.floor(diff / month) + '个月前';
    } else {
        return Math.floor(diff / year) + '年前';
    }
}

/**
 * 平滑滚动到指定元素
 */
function scrollToElement(element, offset = 0) {
    const target = $(element);
    if (target.length) {
        $('html, body').animate({
            scrollTop: target.offset().top - offset
        }, 800);
    }
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 计算网站运行时间
function calculateRunTime() {
    const startTime = new Date('2025-06-04 00:00:00').getTime();
    const now = new Date().getTime();
    const diff = now - startTime;

    const years = Math.floor(diff / (1000 * 60 * 60 * 24 * 365));
    const days = Math.floor((diff % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    let result = '';
    if (years > 0) {
        result += `${years}年`;
    }
    if (days > 0) {
        result += `${days}天`;
    }
    if (hours > 0) {
        result += `${hours}时`;
    }

    // 如果全为0，显示“刚刚上线”
    if (!result) {
        result = '刚刚上线';
    }

    var runTimeElement = document.getElementById('runTime');
    if (runTimeElement) {
        runTimeElement.textContent = result;
    }
}

$(function () {
    // Back to top
    var $backToTop = $(".back-to-top-btn");
    $(window).scroll(function () {
        if ($(window).scrollTop() > 100) {
            $backToTop.fadeIn(300);
        } else {
            $backToTop.fadeOut(300);
        }
    });
    $backToTop.click(function () {
        $('html,body').animate({scrollTop: 0}, 400);
    });
    // runtime
    function show_runtime() {
        window.setTimeout(show_runtime, 1000);
        var X = new Date("06/04/2025 00:00:00");
        var Y = new Date();
        var T = (Y.getTime() - X.getTime());
        var M = 24 * 60 * 60 * 1000;
        var a = T / M;
        var A = Math.floor(a);
        var b = (a - A) * 24;
        var B = Math.floor(b);
        var c = (b - B) * 60;
        var C = Math.floor(c);
        var d = (c - C) * 60;
        var D = Math.floor(d);
        $("#runtime").html(A + "天" + B + "小时" + C + "分" + D + "秒");
    }
    show_runtime();

    // 打字机动画（中英文切换）
    var cn = $('#typing-text').data('cn') || '';
    var en = $('#typing-text').data('en') || '';
    var texts = [cn, en];
    var idx = 0, charIdx = 0, isDeleting = false;
    var typingSpeed = 90, pause = 1200;
    function typeLoop() {
        var text = texts[idx];
        var shown = isDeleting ? text.substring(0, charIdx--) : text.substring(0, charIdx++);
        var $el = $('#typing-text');
        $el.text(shown);
        if (idx === 1) {
            $el.addClass('text-secondary');
        } else {
            $el.removeClass('text-secondary');
        }
        if (!isDeleting && charIdx > text.length) {
            isDeleting = true;
            setTimeout(typeLoop, pause);
        } else if (isDeleting && charIdx < 0) {
            isDeleting = false;
            idx = (idx + 1) % texts.length;
            setTimeout(typeLoop, 400);
        } else {
            setTimeout(typeLoop, isDeleting ? 40 : typingSpeed);
        }
    }
    typeLoop();
});

// 标签云动态渲染
$(function(){
  var $cloud = $('#tag-cloud');
  if($cloud.length){
    $.getJSON('/index/tagcloud', function(res){
      if(res.code === 1 && res.data && res.data.length){
        var tags = res.data;
        // 计算最大最小
        var counts = tags.map(function(t){return t.count;});
        var min = Math.min.apply(null, counts);
        var max = Math.max.apply(null, counts);
        function getSize(count){
          if(max === min) return 24;
          return 14 + (count - min) * (36 - 14) / (max - min);
        }
        // 打乱顺序
        tags = tags.sort(function(){return Math.random()-0.5;});
        $cloud.html(tags.map(function(tag){
          return '<a href="/index/tag_list/id/'+tag.id+'" style="font-size:'+getSize(tag.count)+'px;color:'+(tag.color||'#7b8a9a')+'">'+tag.name+'</a>';
        }).join(' '));
      }
    });
  }
});

