define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'comment/index' + location.search,
                    add_url: 'comment/add',
                    edit_url: 'comment/edit',
                    del_url: 'comment/del',
                    multi_url: 'comment/multi',
                    import_url: 'comment/import',
                    table: 'comment',
                }
            });

            var table = $("#table");

            // 初始化树形表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'createtime',
                sortOrder: 'desc',
                pagination: false, // 树形结构不使用分页
                search: true,
                searchOnEnterKey: true,
                showRefresh: true,
                showToggle: false,
                showColumns: false,
                fixedColumns: true,
                fixedRightNumber: 1,
                treeShowField: 'content_preview', // 树形展示字段
                parentIdField: '_parent_id', // 父ID字段
                columns: [
                    [
                        {checkbox: true, width: 50},
                        {field: 'id', title: 'ID', width: 60, sortable: true},
                        {
                            field: 'content_preview',
                            title: '评论内容',
                            width: 300,
                            formatter: function(value, row, index) {
                                var html = '';

                                // 添加树形缩进
                                if (row._level > 0) {
                                    html += '<span style="margin-left: ' + (row._level * 20) + 'px;">';
                                    html += '<i class="fa fa-reply text-muted"></i> ';
                                } else {
                                    html += '<span>';
                                }

                                // 评论内容
                                html += '<div class="comment-tree-content">';
                                html += '<div class="content-text">' + (value || '') + '</div>';
                                html += '</div>';
                                html += '</span>';

                                return html;
                            }
                        },
                        {
                            field: 'user_display',
                            title: '用户',
                            width: 150,
                            formatter: function(value, row, index) {
                                var html = '<div class="user-info">';
                                html += '<div class="nickname">' + row.nickname;
                                if (row.is_admin == 1) {
                                    html += ' <span class="label label-success">博主</span>';
                                }
                                html += '</div>';
                                if (row.email) {
                                    html += '<div class="email text-muted" style="font-size:12px;">' + row.email + '</div>';
                                }
                                html += '</div>';
                                return html;
                            }
                        },
                        {
                            field: 'type_display',
                            title: '类型',
                            width: 100,
                            searchList: {"article":"文章评论","message":"留言","about":"关于页面","links":"友链页面"},
                            formatter: function(value, row, index) {
                                var colorMap = {
                                    '文章评论': 'primary',
                                    '留言': 'info',
                                    '关于页面': 'warning',
                                    '友链页面': 'success'
                                };
                                var color = colorMap[value] || 'default';
                                return '<span class="label label-' + color + '">' + value + '</span>';
                            }
                        },
                        {
                            field: 'related_display',
                            title: '关联内容',
                            width: 150,
                            formatter: function(value, row, index) {
                                if (row.type === 'article' && row.article_title) {
                                    return '<span class="text-primary" title="' + row.article_title + '">' +
                                           (row.article_title.length > 20 ? row.article_title.substring(0, 20) + '...' : row.article_title) +
                                           '</span>';
                                }
                                return '<span class="text-muted">' + value + '</span>';
                            }
                        },
                        {
                            field: 'likes',
                            title: '点赞',
                            width: 60,
                            formatter: function(value, row, index) {
                                return value > 0 ? '<span class="text-danger"><i class="fa fa-heart"></i> ' + value + '</span>' : '0';
                            }
                        },
                        {
                            field: 'status',
                            title: '状态',
                            width: 80,
                            searchList: {"normal":"正常","hidden":"隐藏"},
                            formatter: function(value, row, index) {
                                var statusMap = {
                                    'normal': {text: '正常', class: 'success'},
                                    'hidden': {text: '隐藏', class: 'danger'}
                                };
                                var status = statusMap[value] || {text: value, class: 'default'};
                                return '<span class="label label-' + status.class + '">' + status.text + '</span>';
                            }
                        },
                        {
                            field: 'createtime_display',
                            title: '创建时间',
                            width: 140,
                            sortable: true
                        },
                        {
                            field: 'operate',
                            title: '操作',
                            width: 150,
                            table: table,
                            events: $.extend({}, Table.api.events.operate, {
                                'click .btn-reply': function (e, value, row, index) {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    var url = 'comment/reply?ids=' + row.id;
                                    Fast.api.open(url, '博主回复', {
                                        area: ['900px', '700px'],
                                        callback: function(data) {
                                            table.bootstrapTable('refresh');
                                        }
                                    });
                                    return false;
                                }
                            }),
                            formatter: function(value, row, index) {
                                var table = this.table;
                                var options = table ? table.bootstrapTable('getOptions') : {};
                                var pk = options.pk || 'id';
                                var value = row[pk];
                                var html = [];

                                // 编辑按钮
                                if (options['data-operate-edit'] !== false) {
                                    html.push('<a href="javascript:;" class="btn btn-xs btn-success btn-editone" data-id="' + value + '" title="编辑"><i class="fa fa-pencil"></i></a>');
                                }

                                // 回复按钮
                                html.push('<a href="javascript:;" class="btn btn-xs btn-info btn-reply" data-id="' + value + '" title="博主回复"><i class="fa fa-reply"></i></a>');

                                // 删除按钮
                                if (options['data-operate-del'] !== false) {
                                    html.push('<a href="javascript:;" class="btn btn-xs btn-danger btn-delone" data-id="' + value + '" title="删除"><i class="fa fa-trash"></i></a>');
                                }

                                return html.join(' ');
                            }
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定工具栏回复按钮事件
            $(document).on('click', '.btn-reply', function() {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error('请选择要回复的评论');
                    return;
                }
                if (ids.length > 1) {
                    Toastr.error('一次只能回复一条评论');
                    return;
                }

                var url = 'comment/reply?ids=' + ids[0];
                Fast.api.open(url, '博主回复', {
                    area: ['900px', '700px'],
                    callback: function(data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });

            // 表格加载完成后的处理
            table.on('load-success.bs.table', function (e, data) {
                // 为树形结构添加数据属性
                table.find('tbody tr').each(function(index) {
                    var row = data.rows[index];
                    if (row) {
                        $(this).attr('data-parent-id', row._parent_id || 'null');
                        $(this).attr('data-level', row._level || 0);
                        $(this).attr('data-id', row.id);
                    }
                });

                // 添加展开/折叠功能（如果需要的话）
                Controller.addTreeToggle();
            });

            // 搜索功能增强
            table.on('search.bs.table', function (e, text) {
                // 搜索时展开所有节点以显示匹配结果
                table.find('tbody tr').show();
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        // 添加树形表格的展开/折叠功能
        addTreeToggle: function() {
            var table = $("#table");

            // 为父评论添加展开/折叠图标
            table.find('tbody tr[data-parent-id="null"]').each(function() {
                var parentId = $(this).attr('data-id');
                var hasChildren = table.find('tbody tr[data-parent-id="' + parentId + '"]').length > 0;

                if (hasChildren) {
                    var $firstCell = $(this).find('td:eq(1)'); // ID列
                    var $toggleIcon = $('<i class="fa fa-minus-square tree-toggle" style="cursor:pointer;margin-right:5px;color:#337ab7;" data-parent-id="' + parentId + '"></i>');
                    $firstCell.prepend($toggleIcon);
                }
            });

            // 绑定展开/折叠事件
            table.off('click.tree-toggle').on('click.tree-toggle', '.tree-toggle', function(e) {
                e.stopPropagation();
                var parentId = $(this).attr('data-parent-id');
                var $children = table.find('tbody tr[data-parent-id="' + parentId + '"]');
                var isExpanded = $(this).hasClass('fa-minus-square');

                if (isExpanded) {
                    // 折叠
                    $children.hide();
                    $(this).removeClass('fa-minus-square').addClass('fa-plus-square');
                } else {
                    // 展开
                    $children.show();
                    $(this).removeClass('fa-plus-square').addClass('fa-minus-square');
                }
            });
        },
        reply: function () {
            // 使用FastAdmin标准的表单绑定
            Controller.api.bindevent();

            // 自动聚焦到文本框
            $('#c-content').focus();
        },
        edit: function () {
            // 编辑页面的表单绑定
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
