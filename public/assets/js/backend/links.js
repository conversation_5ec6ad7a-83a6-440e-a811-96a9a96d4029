define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'links/index' + location.search,
                    add_url: 'links/add',
                    edit_url: 'links/edit',
                    del_url: 'links/del',
                    multi_url: 'links/multi',
                    import_url: 'links/import',
                    table: 'links',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'type', title: '类型', searchList: {"friend":"友链","favorite":"收藏推荐"}, formatter: Table.api.formatter.normal},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'url', title: __('Url'), operate: 'LIKE', formatter: Table.api.formatter.url},
                        {field: 'logo', title: __('Logo'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'description', title: __('Description'), operate: 'LIKE'},
                        {field: 'email', title: __('Email'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"0":"待审核","1":"已通过","2":"已拒绝"}, formatter: function(value, row, index) {
                            var colorClass = '';
                            var text = '';
                            // 将值转换为字符串进行比较，支持整数和字符串类型
                            var statusValue = String(value);
                            switch(statusValue) {
                                case '0':
                                    colorClass = 'warning';
                                    text = '待审核';
                                    break;
                                case '1':
                                    colorClass = 'success';
                                    text = '已通过';
                                    break;
                                case '2':
                                    colorClass = 'danger';
                                    text = '已拒绝';
                                    break;
                                default:
                                    colorClass = 'default';
                                    text = '未知';
                            }
                            return '<span class="label label-' + colorClass + '">' + text + '</span>';
                        }},
                        {field: 'reject_reason', title: __('Reject_reason'), operate: 'LIKE', visible: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
