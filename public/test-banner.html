<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner高度测试</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/blog.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f6fa;
        }
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <div>Banner高度测试</div>
        <div id="banner-height"></div>
    </div>

    <!-- 测试 banner-section -->
    <div class="banner-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="banner-content">
                        <h1>测试 Banner Section</h1>
                        <p>这个Banner的高度应该设置为550px</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="banner-stats">
                        <div class="stat-item">
                            <h3>100</h3>
                            <p>文章</p>
                        </div>
                        <div class="stat-item">
                            <h3>50</h3>
                            <p>评论</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试 blog-banner -->
    <div class="blog-banner">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 text-center">
                    <h1 class="banner-title">测试 Blog Banner</h1>
                    <div class="banner-line"></div>
                    <div class="blog-typing">
                        <span id="typing-text">这个Banner的高度应该设置为550px</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2>Banner高度测试结果</h2>
                <p>上面的两个Banner都应该显示为550px高度。</p>
                <div class="alert alert-info">
                    <strong>测试说明：</strong><br>
                    1. 第一个Banner使用 .banner-section 类<br>
                    2. 第二个Banner使用 .blog-banner 类<br>
                    3. 两个Banner的高度都应该设置为550px
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检测Banner高度
        function checkBannerHeight() {
            const bannerSection = document.querySelector('.banner-section');
            const blogBanner = document.querySelector('.blog-banner');
            
            const bannerSectionHeight = bannerSection.offsetHeight;
            const blogBannerHeight = blogBanner.offsetHeight;
            
            document.getElementById('banner-height').innerHTML = 
                `Banner Section: ${bannerSectionHeight}px<br>` +
                `Blog Banner: ${blogBannerHeight}px`;
        }
        
        // 页面加载后检测
        window.addEventListener('load', checkBannerHeight);
        window.addEventListener('resize', checkBannerHeight);
    </script>
</body>
</html> 