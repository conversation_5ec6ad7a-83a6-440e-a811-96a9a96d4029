const CACHE_NAME = 'fastadmin-dynamic-cache-v3';
const urlsToCache = [
  '/',
  // '/message',
  '/tag',
  '/links',
  '/category',
  '/archive',
  '/about',
  '/assets/css/blog.css',
  '/assets/css/bootstrap.min.css',
  '/assets/js/bootstrap-4.6.2-dist/css/bootstrap.min.css',
  '/assets/js/bootstrap-4.6.2-dist/js/bootstrap.min.js',
  '/assets/js/jquery@3.5.1/jquery.min.js',
  '/assets/js/frontend.js',
  '/assets/js/require.js',
  '/uploads/20250627/a68d5863c4efdfd519f25c58a4a4d874.png'
  // ...你常用的静态资源
];

// install 阶段只缓存基础资源
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME).then(function(cache) {
      return cache.addAll(urlsToCache);
    })
  );
});

// fetch 阶段动态缓存所有访问过的页面和资源
// self.addEventListener('fetch', function(event) {
//   event.respondWith(
//     caches.match(event.request).then(function(response) {
//       if (response) {
//         return response;
//       }
//       return fetch(event.request).then(function(networkResponse) {
//         // 只缓存GET和200响应
//         if (
//           event.request.method === 'GET' &&
//           networkResponse &&
//           networkResponse.status === 200 &&
//           networkResponse.type === 'basic'
//         ) {
//           // clone 一次用于缓存
//           const responseToCache = networkResponse.clone();
//           caches.open(CACHE_NAME).then(function(cache) {
//             cache.put(event.request, responseToCache);
//           });
//         }
//         return networkResponse;
//       }).catch(function() {
//         // 可以自定义离线页面
//         // return caches.match('/offline.html');
//       });
//     })
//   );
// });

// 激活时清理旧缓存
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.filter(function(name) {
          return name !== CACHE_NAME;
        }).map(function(name) {
          return caches.delete(name);
        })
      );
    })
  );
});