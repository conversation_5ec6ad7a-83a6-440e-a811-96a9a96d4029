<?php
/**
 * 测试网站链接功能
 */

// 数据库配置
$host = 'localhost';
$dbname = 'deshengdulikaifa';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

echo "=== 测试网站链接功能 ===\n\n";

// 1. 创建测试数据
echo "1. 创建测试数据...\n";

// 创建有网站的父评论
$stmt = $pdo->prepare("INSERT INTO fa_comment (content, nickname, email, website, type, parent_id, reply_to_id, status, createtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
$stmt->execute([
    '这是有网站的父评论',
    '网站用户A',
    '<EMAIL>',
    'https://example.com',
    'message',
    0,
    0,
    'normal',
    time()
]);
$parentWithWebsiteId = $pdo->lastInsertId();
echo "✓ 有网站的父评论创建成功，ID: $parentWithWebsiteId\n";

// 创建没有网站的父评论
$stmt->execute([
    '这是没有网站的父评论',
    '普通用户B',
    '<EMAIL>',
    '',
    'message',
    0,
    0,
    'normal',
    time()
]);
$parentWithoutWebsiteId = $pdo->lastInsertId();
echo "✓ 没有网站的父评论创建成功，ID: $parentWithoutWebsiteId\n";

// 创建有网站的子评论
$stmt->execute([
    '这是有网站的子评论',
    '网站用户C',
    '<EMAIL>',
    'https://blog.example.com',
    'message',
    $parentWithWebsiteId,
    0,
    'normal',
    time()
]);
$childWithWebsiteId = $pdo->lastInsertId();
echo "✓ 有网站的子评论创建成功，ID: $childWithWebsiteId\n";

// 创建没有网站的子评论
$stmt->execute([
    '这是没有网站的子评论',
    '普通用户D',
    '<EMAIL>',
    '',
    'message',
    $parentWithWebsiteId,
    0,
    'normal',
    time()
]);
$childWithoutWebsiteId = $pdo->lastInsertId();
echo "✓ 没有网站的子评论创建成功，ID: $childWithoutWebsiteId\n";

// 2. 测试查询结果
echo "\n2. 测试查询结果...\n";

// 查询所有父评论
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE parent_id = 0 AND type = 'message' AND status = 'normal' ORDER BY createtime DESC");
$stmt->execute();
$parentComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "父评论列表：\n";
foreach ($parentComments as $comment) {
    echo "  - ID: {$comment['id']}\n";
    echo "    昵称: {$comment['nickname']}\n";
    echo "    网站: " . ($comment['website'] ? $comment['website'] : '无') . "\n";
    echo "    内容: {$comment['content']}\n";
    echo "\n";
}

// 查询所有子评论
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE parent_id > 0 AND type = 'message' AND status = 'normal' ORDER BY createtime DESC");
$stmt->execute();
$childComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "子评论列表：\n";
foreach ($childComments as $comment) {
    echo "  - ID: {$comment['id']}\n";
    echo "    昵称: {$comment['nickname']}\n";
    echo "    网站: " . ($comment['website'] ? $comment['website'] : '无') . "\n";
    echo "    内容: {$comment['content']}\n";
    echo "    父评论ID: {$comment['parent_id']}\n";
    echo "\n";
}

// 3. 模拟前端显示
echo "3. 模拟前端显示...\n";

echo "有网站的昵称HTML：\n";
foreach ($parentComments as $comment) {
    if ($comment['website']) {
        echo "  <a href=\"{$comment['website']}\" target=\"_blank\" class=\"text-decoration-none\">{$comment['nickname']}</a>\n";
    } else {
        echo "  {$comment['nickname']}\n";
    }
}

echo "\n子评论昵称HTML：\n";
foreach ($childComments as $comment) {
    if ($comment['website']) {
        echo "  <a href=\"{$comment['website']}\" target=\"_blank\" class=\"text-decoration-none\">{$comment['nickname']}</a>\n";
    } else {
        echo "  {$comment['nickname']}\n";
    }
}

echo "\n✓ 测试完成！\n";
?> 