<?php
/**
 * 测试回复功能
 */

// 数据库配置
$host = 'localhost';
$dbname = 'deshengdulikaifa';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

echo "=== 测试回复功能 ===\n\n";

// 1. 创建测试数据
echo "1. 创建测试数据...\n";

// 创建父评论
$stmt = $pdo->prepare("INSERT INTO fa_comment (content, nickname, email, type, parent_id, reply_to_id, status, createtime) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
$stmt->execute([
    '这是第一条父评论',
    '用户A',
    '<EMAIL>',
    'message',
    0,
    0,
    'normal',
    time()
]);
$parentId = $pdo->lastInsertId();
echo "✓ 父评论创建成功，ID: $parentId\n";

// 创建第一个子评论（回复父评论）
$stmt->execute([
    '这是回复父评论的子评论',
    '用户B',
    '<EMAIL>',
    'message',
    $parentId,
    0,
    'normal',
    time()
]);
$firstChildId = $pdo->lastInsertId();
echo "✓ 第一个子评论创建成功，ID: $firstChildId\n";

// 创建回复第一个子评论的评论
$stmt->execute([
    '这是回复子评论的评论',
    '用户C',
    '<EMAIL>',
    'message',
    $parentId,
    $firstChildId,
    'normal',
    time()
]);
$replyToFirstId = $pdo->lastInsertId();
echo "✓ 回复第一个子评论的评论创建成功，ID: $replyToFirstId\n";

// 2. 测试查询回复信息
echo "\n2. 测试查询回复信息...\n";

// 查询所有子评论，包括回复信息
$stmt = $pdo->prepare("
    SELECT c.*, r.nickname as reply_to_nickname, r.content as reply_to_content 
    FROM fa_comment c 
    LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
    WHERE c.parent_id = ? AND c.type = 'message' AND c.status = 'normal'
    ORDER BY c.createtime ASC
");
$stmt->execute([$parentId]);
$children = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "父评论 $parentId 的所有子评论：\n";
foreach ($children as $child) {
    echo "  - ID: {$child['id']}\n";
    echo "    内容: {$child['content']}\n";
    echo "    用户: {$child['nickname']}\n";
    echo "    parent_id: {$child['parent_id']}\n";
    echo "    reply_to_id: {$child['reply_to_id']}\n";
    
    if ($child['reply_to_nickname']) {
        echo "    回复用户: {$child['reply_to_nickname']}\n";
        echo "    回复内容: {$child['reply_to_content']}\n";
    } else {
        echo "    回复类型: 回复父评论\n";
    }
    echo "\n";
}

// 3. 测试API接口
echo "3. 测试API接口...\n";

// 模拟API请求获取子评论
$stmt = $pdo->prepare("
    SELECT c.*, r.nickname as reply_to_nickname, r.content as reply_to_content 
    FROM fa_comment c 
    LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
    WHERE c.parent_id = ? AND c.type = 'message' AND c.status = 'normal'
    ORDER BY c.createtime DESC
");
$stmt->execute([$parentId]);
$apiChildren = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "API返回的子评论数据：\n";
foreach ($apiChildren as $child) {
    $isAdmin = in_array(strtolower(trim($child['email'])), ['<EMAIL>']);
    $avatarUrl = "https://cravatar.cn/avatar/" . md5(strtolower(trim($child['email']))) . "?d=identicon";
    $createtimeFormatted = date('Y-m-d H:i', $child['createtime']);
    $contentFormatted = htmlspecialchars($child['content']);
    
    echo "  - ID: {$child['id']}\n";
    echo "    昵称: {$child['nickname']}\n";
    echo "    邮箱: {$child['email']}\n";
    echo "    是否管理员: " . ($isAdmin ? '是' : '否') . "\n";
    echo "    头像: $avatarUrl\n";
    echo "    时间: $createtimeFormatted\n";
    echo "    内容: $contentFormatted\n";
    
    if ($child['reply_to_nickname']) {
        echo "    回复用户: {$child['reply_to_nickname']}\n";
        echo "    回复内容: {$child['reply_to_content']}\n";
    }
    echo "\n";
}

echo "✓ 测试完成！\n";
?> 