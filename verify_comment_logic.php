<?php
/**
 * 验证评论逻辑
 */

// 数据库配置
$host = 'localhost';
$dbname = 'deshengdulikaifa';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

echo "=== 评论逻辑验证 ===\n\n";

// 1. 检查父评论（最上面提交的留言）
echo "1. 父评论逻辑验证：\n";
$stmt = $pdo->prepare("SELECT * FROM fa_comment WHERE parent_id = 0 AND reply_to_id = 0 AND type = 'message' ORDER BY createtime DESC LIMIT 5");
$stmt->execute();
$parentComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "父评论列表（parent_id=0, reply_to_id=0）：\n";
foreach ($parentComments as $comment) {
    echo "  - ID: {$comment['id']}, 内容: {$comment['content']}\n";
}

// 2. 检查子评论（回复父评论）
echo "\n2. 子评论逻辑验证：\n";
$stmt = $pdo->prepare("SELECT c.*, p.content as parent_content FROM fa_comment c 
                       LEFT JOIN fa_comment p ON c.parent_id = p.id 
                       WHERE c.parent_id > 0 AND c.reply_to_id = 0 AND c.type = 'message' 
                       ORDER BY c.createtime DESC LIMIT 5");
$stmt->execute();
$childComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "子评论列表（parent_id>0, reply_to_id=0）：\n";
foreach ($childComments as $comment) {
    echo "  - ID: {$comment['id']}, 内容: {$comment['content']}, 父评论: {$comment['parent_content']}\n";
}

// 3. 检查回复子评论的评论
echo "\n3. 回复子评论逻辑验证：\n";
$stmt = $pdo->prepare("SELECT c.*, p.content as parent_content, r.content as reply_to_content FROM fa_comment c 
                       LEFT JOIN fa_comment p ON c.parent_id = p.id 
                       LEFT JOIN fa_comment r ON c.reply_to_id = r.id 
                       WHERE c.parent_id > 0 AND c.reply_to_id > 0 AND c.type = 'message' 
                       ORDER BY c.createtime DESC LIMIT 5");
$stmt->execute();
$replyComments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "回复子评论列表（parent_id>0, reply_to_id>0）：\n";
foreach ($replyComments as $comment) {
    echo "  - ID: {$comment['id']}, 内容: {$comment['content']}, 父评论: {$comment['parent_content']}, 回复: {$comment['reply_to_content']}\n";
}

// 4. 统计各类型评论数量
echo "\n4. 评论类型统计：\n";
$stmt = $pdo->query("SELECT 
    COUNT(CASE WHEN parent_id = 0 AND reply_to_id = 0 THEN 1 END) as parent_comments,
    COUNT(CASE WHEN parent_id > 0 AND reply_to_id = 0 THEN 1 END) as child_comments,
    COUNT(CASE WHEN parent_id > 0 AND reply_to_id > 0 THEN 1 END) as reply_comments,
    COUNT(*) as total_comments
FROM fa_comment WHERE type = 'message'");
$stats = $stmt->fetch(PDO::FETCH_ASSOC);

echo "总评论数: {$stats['total_comments']}\n";
echo "父评论数: {$stats['parent_comments']}\n";
echo "子评论数: {$stats['child_comments']}\n";
echo "回复子评论数: {$stats['reply_comments']}\n";

echo "\n✓ 验证完成！\n";
?> 