<?php
/**
 * 测试回复信息中的网站链接功能
 */

// 模拟测试数据

echo "=== 测试回复信息中的网站链接功能 ===\n\n";

// 测试场景1：回复到有网站的用户
echo "测试场景1：回复到有网站的用户\n";
$testData1 = [
    'reply_to_nickname' => '张三',
    'reply_to_website' => 'https://zhangsan.com',
    'reply_to_content' => '这是一个很好的观点'
];

echo "回复信息：回复 @";
if (!empty($testData1['reply_to_website'])) {
    echo "<a href=\"{$testData1['reply_to_website']}\" target=\"_blank\" class=\"text-decoration-none\">{$testData1['reply_to_nickname']}</a>";
} else {
    echo $testData1['reply_to_nickname'];
}
echo "：{$testData1['reply_to_content']}\n\n";

// 测试场景2：回复到没有网站的用户
echo "测试场景2：回复到没有网站的用户\n";
$testData2 = [
    'reply_to_nickname' => '李四',
    'reply_to_website' => '',
    'reply_to_content' => '我同意你的看法'
];

echo "回复信息：回复 @";
if (!empty($testData2['reply_to_website'])) {
    echo "<a href=\"{$testData2['reply_to_website']}\" target=\"_blank\" class=\"text-decoration-none\">{$testData2['reply_to_nickname']}</a>";
} else {
    echo $testData2['reply_to_nickname'];
}
echo "：{$testData2['reply_to_content']}\n\n";

// 测试场景3：JavaScript模板中的逻辑
echo "测试场景3：JavaScript模板逻辑\n";
$jsTestData = [
    [
        'reply_to_nickname' => '王五',
        'reply_to_website' => 'https://wangwu.net',
        'reply_to_content' => '很有见地'
    ],
    [
        'reply_to_nickname' => '赵六',
        'reply_to_website' => '',
        'reply_to_content' => '学习了'
    ]
];

foreach ($jsTestData as $index => $data) {
    echo "数据" . ($index + 1) . "：\n";
    echo "回复 @";
    echo $data['reply_to_website'] ? 
        "<a href=\"{$data['reply_to_website']}\" target=\"_blank\" class=\"text-decoration-none\">{$data['reply_to_nickname']}</a>" : 
        $data['reply_to_nickname'];
    echo "：{$data['reply_to_content']}\n\n";
}

echo "=== 测试完成 ===\n";
echo "预期结果：\n";
echo "1. 有网站的用户名显示为可点击链接\n";
echo "2. 没有网站的用户名显示为普通文本\n";
echo "3. 链接在新窗口打开\n";
echo "4. 链接样式与普通昵称保持一致\n";
?> 