-- 文章表
CREATE TABLE `fa_article` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
  `content` longtext COMMENT '内容',
  `summary` varchar(500) DEFAULT '' COMMENT '摘要',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '分类ID',
  `image` varchar(255) DEFAULT '' COMMENT '封面图',
  `views` int(10) unsigned DEFAULT '0' COMMENT '阅读量',
  `likes` int(10) unsigned DEFAULT '0' COMMENT '点赞数',
  `is_recommend` tinyint(1) unsigned DEFAULT '0' COMMENT '是否推荐',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 分类表
CREATE TABLE `fa_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `description` varchar(255) DEFAULT '' COMMENT '分类描述',
  `sort` int(10) unsigned DEFAULT '0' COMMENT '排序',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 标签表
CREATE TABLE `fa_tag` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#007bff' COMMENT '标签颜色',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 文章标签关联表
CREATE TABLE `fa_article_tag` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `article_id` int(10) unsigned NOT NULL COMMENT '文章ID',
  `tag_id` int(10) unsigned NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `article_tag` (`article_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 评论表
CREATE TABLE `fa_comment` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `content` text NOT NULL COMMENT '评论内容',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `website` varchar(255) DEFAULT '' COMMENT '网站',
  `type` enum('article','message','about','links') NOT NULL DEFAULT 'article' COMMENT '评论类型',
  `article_id` int(10) unsigned DEFAULT '0' COMMENT '文章ID',
  `parent_id` int(10) unsigned DEFAULT '0' COMMENT '父评论ID',
  `likes` int(10) unsigned DEFAULT '0' COMMENT '点赞数',
  `is_admin` tinyint(1) unsigned DEFAULT '0' COMMENT '是否管理员回复',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `article_id` (`article_id`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 评论点赞表
CREATE TABLE `fa_comment_like` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `comment_id` int(10) unsigned NOT NULL COMMENT '评论ID',
  `ip` varchar(45) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `comment_ip` (`comment_id`,`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 友链表
CREATE TABLE `fa_links` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '网站名称',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '网站链接',
  `logo` varchar(255) DEFAULT '' COMMENT '网站Logo',
  `description` varchar(255) DEFAULT '' COMMENT '网站描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `type` varchar(20) NOT NULL DEFAULT 'friend' COMMENT '类型(friend=友链,favorite=收藏推荐)',
  `createtime` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='友链表';

-- 插入示例数据
INSERT INTO `fa_category` (`name`, `description`, `sort`, `status`, `createtime`) VALUES
('技术分享', '分享各种技术心得和经验', 1, 'normal', UNIX_TIMESTAMP()),
('独立开发', '独立开发项目经验分享', 2, 'normal', UNIX_TIMESTAMP()),
('生活随笔', '生活中的感悟和思考', 3, 'normal', UNIX_TIMESTAMP()),
('工具推荐', '好用的工具和软件推荐', 4, 'normal', UNIX_TIMESTAMP());

INSERT INTO `fa_tag` (`name`, `color`, `status`, `createtime`) VALUES
('PHP', '#007bff', 'normal', UNIX_TIMESTAMP()),
('JavaScript', '#ffc107', 'normal', UNIX_TIMESTAMP()),
('Python', '#28a745', 'normal', UNIX_TIMESTAMP()),
('Vue.js', '#17a2b8', 'normal', UNIX_TIMESTAMP()),
('React', '#6f42c1', 'normal', UNIX_TIMESTAMP()),
('MySQL', '#fd7e14', 'normal', UNIX_TIMESTAMP()),
('Docker', '#20c997', 'normal', UNIX_TIMESTAMP()),
('Git', '#dc3545', 'normal', UNIX_TIMESTAMP()),
('Linux', '#6c757d', 'normal', UNIX_TIMESTAMP()),
('FastAdmin', '#e83e8c', 'normal', UNIX_TIMESTAMP()); 