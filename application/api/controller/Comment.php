<?php
namespace app\api\controller;

use think\Controller;
use app\common\service\CommentService;
use think\facade\Mail;

class Comment extends Controller
{
    /**
     * 添加评论/留言
     */
    public function add()
    {
        try {
            if (!$this->request->isPost()) {
                return $this->error('请求方式错误');
            }

            $params = $this->request->post();
            // 限制博主邮箱只能在后台回复
            if ($params['email'] === '<EMAIL>') {
                return json(['code' => 0, 'msg' => '博主请在后台管理系统中回复评论']);
            }else if($params['nickname'] === '德胜独立开发' || $params['nickname'] === '德胜独立开发博客' ){
                return json(['code' => 0, 'msg' => '博主请在后台管理系统中回复评论']);
            }
            // 验证评论数据
            $validation = CommentService::validateCommentData($params);
            if ($validation !== true) {
                return json($validation);
            }



            // 获取客户端信息
            $clientInfo = CommentService::getClientInfo($this->request);

            // 构建数据
            $data = [
                'content' => trim($params['content']),
                'nickname' => trim($params['nickname']),
                'email' => trim($params['email']),
                'website' => $params['website'] ?? '',
                'type' => $params['type'] ?? 'message',
                'article_id' => $params['article_id'] ?? 0,
                'parent_id' => $params['parent_id'] ?? 0,
                'reply_to_id' => $params['reply_to_id'] ?? 0,
                'browser' => $clientInfo['browser'],
                'device' => $clientInfo['device'],
                'ip' => $clientInfo['ip'],
                'ip_location' => $clientInfo['ip_location'],
                'status' => 'normal',
                'createtime' => time()
            ];


            // 创建评论并获取评论对象
            $comment = new \app\common\model\Comment();
            $result = $comment->save($data);

            if ($result) {
                // 异步发送邮件通知，不影响评论接口响应速度
                $this->sendEmailNotificationAsync($comment, $data);

                return json(['code' => 1, 'msg' => '评论发布成功']);
            } else {
                return json(['code' => 0, 'msg' => '评论发布失败']);
            }
        } catch (\Throwable $e) {
            return json(['code' => 0, 'msg' => '系统异常：' . $e->getMessage()]);
        }
    }

    // 邮件发送辅助方法
    protected function sendMail($to, $subject, $html)
    {
        try {
            $email = new \app\common\library\Email();
            $result = $email
                ->to($to)
                ->subject($subject)
                ->message($html)
                ->send();
            return '邮件发送成功: ' . $to;
        } catch (\Exception $e) {
            // 邮件发送失败，记录日志但不影响主流程
            error_log('邮件发送失败: ' . $e->getMessage());
            return '邮件发送失败: ' . $e->getMessage() . ' 收件人: ' . $to;
        }
    }

    /**
     * 异步发送邮件通知
     * 使用快速返回的方式，不阻塞评论接口
     */
    protected function sendEmailNotificationAsync($comment, $data)
    {
        // 使用快速返回，让邮件在后台处理
        try {
            $bloggerEmail = '<EMAIL>'; // TODO: 修改为你的邮箱

            if ($comment['reply_to_id'] && $comment['reply_to_id'] != '0' && $comment['reply_to_id'] != 0) {
                $parent = \app\common\model\Comment::get($comment['reply_to_id']);
            } else if ($comment['parent_id'] && $comment['parent_id'] != '0' && $comment['parent_id'] != 0) {
                $parent = \app\common\model\Comment::get($comment['parent_id']);
            } else {
                $parent = null;
            }

            if ($data && $data['type'] == 'article' && $data['article_id']) {
                $article_url = 'https://deshengdulikaifa.com/detail/' . $data['article_id'];
                $article_title = '查看文章';
            } else {
                $article_url = 'https://deshengdulikaifa.com/message';
                $article_title = '查看留言';
            }
            if ($parent) {
                $mailData = [];
                $mailData['title'] = '你的评论有新回复';
                $mailData['subtitle'] = '有人回复了你的评论';
                $mailData['nickname'] = $parent['nickname'] . ' 同学,您曾发表评论:';
                $mailData['content'] = $parent['content'];
                $mailData['article_title'] = $article_title;
                $mailData['article_url'] = $article_url;
                $mailData['blogger_name'] = '德胜独立开发博客';
                $mailData['parent_nickname'] = $data['nickname'];
                $mailData['parent_content'] = $data['content'];
                // 检查是否需要发送邮件
                $shouldSend = $data['email'] != $parent['email'] && $data['email'] != $bloggerEmail;
                if ($shouldSend) {
                    $parentMailHtml = view('api@mail/notify_blogger', $mailData)->getContent();
                    $this->sendMail($parent['email'], $mailData['title'], $parentMailHtml);
                }
            }

            if ($data['email'] != $bloggerEmail) {
                $mailData1 = [];
                $mailData1['title'] = '新消息通知';
                $mailData1['subtitle'] = '你的博客收到了新的评论';
                $mailData1['article_title'] = $article_title;
                $mailData1['article_url'] = $article_url;
                $mailData1['blogger_name'] = '德胜独立开发博客';
                if ($parent) {
                    $mailData1['parent_nickname'] = $data['nickname'];
                    $mailData1['parent_content'] = $data['content'];
                    $mailData1['nickname'] = $parent['nickname'];
                    $mailData1['content'] = $parent['content'];
                } else {
                    $mailData1['nickname'] = $data['nickname'];
                    $mailData1['content'] = $data['content'];
                }
                // 只发送给博主，不发送给评论者自己
                $bloggerMailHtml = view('api@mail/notify_blogger', $mailData1)->getContent();
                $this->sendMail($bloggerEmail, $mailData1['title'], $bloggerMailHtml);
            }

        } catch (\Exception $e) {
            // 异步邮件发送失败，记录日志但不影响主流程
            error_log('异步邮件发送失败: ' . $e->getMessage());
        }
    }
    /**
     * 点赞评论
     */
    public function like()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $commentId = $this->request->post('comment_id');
        if (!$commentId) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $comment = \app\common\model\Comment::get($commentId);
        if (!$comment) {
            return json(['code' => 0, 'msg' => '评论不存在']);
        }

        $comment->setInc('likes', 1); // likes 字段自增1
        return json(['code' => 1, 'msg' => '点赞成功', 'likes' => $comment->likes + 1]);
    }

    /**
     * 获取子评论列表（每次加载5条）
     */
    public function children()
    {
        $parentId = $this->request->param('parent_id', 0);
        $page = max(1, intval($this->request->param('page', 1)));
        $limit = 5;

        if (!$parentId) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $type = $this->request->param('type', 'message');
        $articleId = $this->request->param('article_id', 0);

        $commentModel = new \app\common\model\Comment;
        $where = [
            'c.type' => $type,
            'c.status' => 'normal',
            'c.parent_id' => $parentId
        ];
        if ($type === 'article' && $articleId) {
            $where['c.article_id'] = $articleId;
        }

        $children_count = $commentModel->alias('c')->where($where)->count();
        $offset = ($page - 1) * $limit; // 跳过最早那一条
        $childrenList = $commentModel->alias('c')
            ->join('fa_comment r', 'c.reply_to_id = r.id', 'LEFT')
            ->where($where)
            ->field('c.*, r.nickname as reply_to_nickname, r.content as reply_to_content, r.website as reply_to_website')
            ->order('c.createtime desc')
            ->limit($offset, $limit)
            ->select();

        foreach ($childrenList as &$item) {
            $item['is_admin'] = CommentService::isAdminEmail($item['email']);
            $item['avatar_url'] = CommentService::getAvatarUrl($item['email'], $item['nickname']);
            $item['createtime_formatted'] = date('Y-m-d H:i', $item['createtime']);
            $item['content_formatted'] = CommentService::formatCommentContent($item['content']);
        }
        // 关键：剩余未展示的子评论总数
        $loaded = $page * $limit;
        // 剩余未展示的子评论总数
        $total = max(0, $children_count - $loaded);

        return json([
            'code' => 1,
            'data' => [
                'childrenList' => collection($childrenList)->toArray(),
                'total' => $total,
                'page' => $page,
                'hasMore' => ($offset + $limit - 1) < $children_count
            ]
        ]);
    }
}