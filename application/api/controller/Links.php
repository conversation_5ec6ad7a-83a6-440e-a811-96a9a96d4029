<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

class Links extends Api
{
    protected $noNeedLogin = ['apply', 'list','random'];

    /**
     * 友链申请接口
     */
    public function apply()
    {
        $data = $this->request->post();
        if (empty($data['name']) || empty($data['url'])) {
            $this->error('网站名称和链接必填');
        }
        $now = time();
        $insert = [
            'name' => $data['name'],
            'url' => $data['url'],
            'logo' => isset($data['logo']) ? $data['logo'] : '',
            'description' => isset($data['description']) ? $data['description'] : '',
            'email' => isset($data['email']) ? $data['email'] : '',
            'status' => 0,
            'reject_reason' => '',
            'createtime' => $now,
            'updatetime' => $now
        ];
        Db::name('links')->insert($insert);
        $this->success('提交成功，等待审核');
    }

    /**
     * 获取已通过的友链
     */
    public function list()
    {
        $links = Db::name('links')
            ->where('status', 1)
            ->field('id,name,url,logo,description')
            ->order('id desc')
            ->select();
        $this->success('ok', $links);
    }

    /**
     * 随机返回一个友链
     * GET /api/links/random
     */
    public function random()
    {
        // 先查出所有 status=1 且 type='friend' 的友链 id
        $ids = Db::name('links')
            ->where('status', 1)
            ->where('type', 'friend')
            ->column('id');

        if ($ids && count($ids) > 0) {
            // 用 PHP 随机选一个 id
            $randomId = $ids[array_rand($ids)];
            // 查出该友链
            $link = Db::name('links')
                ->where('id', $randomId)
                ->field('id,name,url,logo,description')
                ->find();
            if ($link) {
                $this->success('ok', $link);
            } else {
                $this->error('暂无可随机访问的友链');
            }
        } else {
            $this->error('暂无可随机访问的友链');
        }
    }
} 