<?php
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Article;
use app\common\model\BlogCategory;
use app\common\model\Tag;

class Search extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function index()
{
    $keyword = $this->request->get('keyword', '', 'trim');
    $page = $this->request->get('page', 1, 'intval');
    $limit = $this->request->get('limit', 10, 'intval');
    $list = [];
    $total = 0;

    if ($keyword) {
        $query = Article::alias('a')
            ->where('a.status', 'normal')
            ->where(function($q) use ($keyword) {
                $q->where('a.title', 'like', "%{$keyword}%")
                  ->whereOr('a.content', 'like', "%{$keyword}%");
            })
            ->field('a.id, a.title, a.content, a.createtime')
            ->order('a.createtime desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        foreach ($list as &$item) {
            $item['content_preview'] = mb_substr(strip_tags($item['content']), 0, 100, 'utf-8') . '...';
            $item['createtime_text'] = date('Y-m-d', $item['createtime']);
        }
    }

    $hasMore = ($page * $limit) < $total;
    $totalPages = ceil($total / $limit);

    $this->success('ok', [
        'list' => $list,
        'keyword' => $keyword,
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'hasMore' => $hasMore,
        'totalPages' => $totalPages
    ]);
}
}