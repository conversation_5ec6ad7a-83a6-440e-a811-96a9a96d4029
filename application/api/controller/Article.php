<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

class Article extends Api
{
    // 允许未登录访问
    protected $noNeedLogin = ['reaction','random'];
    protected $noNeedRight = ['reaction','random'];

    /**
     * 文章表情投票
     * POST: /api/article/reaction
     * 参数: article_id, emoji_key
     */
    public function reaction()
    {
        $article_id = $this->request->post('article_id/d');
        $emoji_key = $this->request->post('emoji_key/s', '');

        // 允许的字段
        $allow = [
            'reaction_agree',
            'reaction_look_down',
            'reaction_sunglasses',
            'reaction_pick_nose',
            'reaction_awkward',
            'reaction_sleep'
        ];

        if (!$article_id || !in_array($emoji_key, $allow)) {
            $this->error('参数错误');
        }

        // 加1操作
        $res = Db::name('article')->where('id', $article_id)->setInc($emoji_key, 1);

        if ($res) {
            // 获取最新数值
            $count = Db::name('article')->where('id', $article_id)->value($emoji_key);
            $this->success('ok', ['count' => $count]);
        } else {
            $this->error('操作失败');
        }
    }
     /**
     * 随机返回一篇文章
     * GET /api/article/random
     */
    public function random()
    {
        // 先查出所有 status='normal' 的文章 id
        $ids = Db::name('article')
            ->where('status', 'normal')
            ->column('id');

        if ($ids && count($ids) > 0) {
            // 用 PHP 随机选一个 id
            $randomId = $ids[array_rand($ids)];
            if ($randomId) {
                $url = url('/detail/' . $randomId);
                // halt([$randomId, $url]);
                $this->success('ok', [
                    'url'   => $url,
                    'id'    => $randomId
                ]);
            } else {
                $this->error('暂无可随机访问的文章');
            }
        } else {
            $this->error('暂无可随机访问的文章');
        }
    }
}