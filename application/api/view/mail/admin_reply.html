<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f6fa;
            color: #333;
            line-height: 1.6;
            padding: 20px 0;
        }
        
        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .email-header {
            background: linear-gradient(135deg, #5cb85c 0%, #4cae4c 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header-icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .email-content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }
        
        .original-comment {
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .original-comment-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .original-comment-content {
            font-size: 16px;
            color: #333;
            line-height: 1.6;
        }
        
        .admin-reply {
            background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
            border-left: 4px solid #5cb85c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .admin-reply-title {
            font-size: 14px;
            color: #5cb85c;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .admin-reply-content {
            font-size: 16px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .admin-signature {
            font-size: 14px;
            color: #666;
            font-style: italic;
            text-align: right;
        }
        
        .article-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .article-info-title {
            font-size: 14px;
            color: #856404;
            margin-bottom: 8px;
        }
        
        .article-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .article-link:hover {
            text-decoration: underline;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #5cb85c 0%, #4cae4c 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(92, 184, 92, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .email-footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .footer-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .footer-brand {
            font-size: 18px;
            font-weight: 600;
            color: #5cb85c;
            margin-bottom: 5px;
        }
        
        .footer-powered {
            font-size: 12px;
            color: #999;
        }
        
        .footer-note {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        @media (max-width: 600px) {
            .email-wrapper {
                margin: 0 10px;
            }
            
            .email-header, .email-content, .email-footer {
                padding: 20px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-header">
            <span class="header-icon">💬</span>
            <h1 class="header-title">{$title}</h1>
            <p class="header-subtitle">{$subtitle}</p>
        </div>
        
        <div class="email-content">
            <div class="greeting">
                您好，{$original_nickname}！
            </div>
            
            <p>您在 <strong>{$blogger_name}</strong> 的评论收到了博主回复：</p>
            
            <div class="original-comment">
                <div class="original-comment-title">您的评论：</div>
                <div class="original-comment-content">{$content|nl2br}</div>
            </div>
            
            <div class="admin-reply">
                <div class="admin-reply-title">🎯 博主回复：</div>
                <div class="admin-reply-content">{$parent_content|nl2br}</div>
                <div class="admin-signature">—— {$parent_nickname}</div>
            </div>
            
            {if isset($article_title) && $article_title && $article_title != '留言板' && $article_title != '关于页面' && $article_title != '友链页面'}
            <div class="article-info">
                <div class="article-info-title">📖 相关文章：</div>
                <a href="{$article_url}" class="article-link" target="_blank">{$article_title}</a>
            </div>
            {/if}
            
            <div class="action-buttons">
                <a href="{$article_url}" class="btn" target="_blank">查看完整对话</a>
                <a href="{:url('index/index', [], true, true)}" class="btn btn-secondary" target="_blank">访问博客</a>
            </div>
        </div>
        
        <div class="email-footer">
            <p class="footer-text">感谢您的留言，期待与您的进一步交流！</p>
            <p class="footer-brand">{$blogger_name}</p>
            <p class="footer-powered">Powered by FastAdmin</p>
            <div class="footer-note">
                此邮件由系统自动发送，请勿直接回复。<br>
                如果您不希望收到此类邮件，请联系网站管理员。
            </div>
        </div>
    </div>
</body>
</html>
