<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你发表的评论已收到</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f6fa;
            color: #333;
            line-height: 1.6;
            padding: 20px 0;
        }
        
        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header-icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .email-content {
            padding: 30px;
        }
        
        .comment-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
            margin-right: 12px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .article-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .article-link:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .parent-reply {
            background-color: #fff3cd;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .parent-reply-text {
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .comment-content {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .comment-text {
            font-size: 16px;
            line-height: 1.7;
            color: #333;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 0 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .footer-brand {
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }
        
        .footer-powered {
            color: #6c757d;
            font-size: 12px;
            margin-top: 8px;
        }
        
        @media (max-width: 600px) {
            .email-wrapper {
                margin: 0 10px;
                border-radius: 6px;
            }
            
            .email-header,
            .email-content {
                padding: 20px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-label {
                margin-bottom: 4px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-header">
            <span class="header-icon">✅</span>
            <h1 class="header-title">评论已收到</h1>
            <p class="header-subtitle">感谢你的留言，我们会认真回复</p>
        </div>
        
        <div class="email-content">
            <div class="comment-info">
                <div class="info-row">
                    <span class="info-label">昵称：</span>
                    <span class="info-value">{$nickname}</span>
                </div>
                {if isset($article_title) && $article_title}
                <div class="info-row">
                    <span class="info-label">文章：</span>
                    <a href="{$article_url}" class="article-link" target="_blank">{$article_title}</a>
                </div>
                {/if}
                <div class="info-row">
                    <span class="info-label">时间：</span>
                    <span class="info-value">{:date('Y-m-d H:i')}</span>
                </div>
            </div>
            
            {if isset($parent_nickname) && $parent_nickname}
            <div class="parent-reply">
                <div class="parent-reply-text">
                    回复 <strong>{$parent_nickname}</strong>：{$parent_content|mb_substr=0,80}...
                </div>
            </div>
            {/if}
            
            <div class="comment-content">
                <div class="comment-text">{$content}</div>
            </div>
            
            <div class="action-buttons">
                <a href="{$article_url}" class="btn" target="_blank">查看文章</a>
                <a href="{:url('index/index', [], true, true)}" class="btn" target="_blank">访问博客</a>
            </div>
        </div>
        
        <div class="email-footer">
            <p class="footer-text">感谢你的留言，欢迎常来交流！</p>
            <p class="footer-brand">德胜独立开发博客</p>
            <p class="footer-powered">Powered by FastAdmin</p>
        </div>
    </div>
</body>
</html> 