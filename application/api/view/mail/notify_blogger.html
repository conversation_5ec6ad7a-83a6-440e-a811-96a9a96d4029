<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f6fa;
            color: #333;
            line-height: 1.6;
            padding: 20px 0;
        }
        
        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            display: block;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .email-content {
            padding: 30px;
        }
        
        .comment-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            box-sizing: border-box;
            margin-bottom: 20px;
        }
        
        .info-row {
            margin-bottom: 12px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #333;
            display: inline-block;
            width: 80px;
            text-align: right;
            margin-right: 8px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
            display: inline;
        }
        
        .article-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .article-link:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .original-comment {
            margin-bottom: 20px;
        }
        
        .original-comment-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .original-comment-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .original-comment-row {
            margin-bottom: 8px;
            display: flex;
        }
        
        .original-comment-row:last-child {
            margin-bottom: 0;
        }
        
        .original-comment-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
            margin-right: 8px;
        }
        
        .original-comment-value {
            color: #333;
            flex: 1;
        }
        
        .original-comment-value a {
            color: #667eea;
            text-decoration: none;
        }
        
        .reply-section {
            margin-bottom: 20px;
        }
        
        .reply-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .reply-content {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            color: #333;
            line-height: 1.6;
        }
        
        .comment-content {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .comment-text {
            font-size: 16px;
            line-height: 1.7;
            color: #333;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 0 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .footer-brand {
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }
        
        .footer-powered {
            color: #6c757d;
            font-size: 12px;
            margin-top: 8px;
        }
        
        @media (max-width: 600px) {
            .email-wrapper {
                margin: 0 10px;
                border-radius: 6px;
            }
            
            .email-header,
            .email-content {
                padding: 20px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .info-row {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            
            .info-label {
                display: inline-block;
                margin-right: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-header">
            <h1 class="header-title">{$title}</h1>
            <p class="header-subtitle">{$subtitle}</p>
        </div>
        
        <div class="email-content">
            <!-- 示例：如果是回复子评论，会显示以下内容 -->
            <div class="original-comment">
                <div class="original-comment-title">{$nickname}</div>
                <div class="original-comment-box">
                    {$content}
                </div>
            </div>
            {if isset($parent_nickname) && $parent_nickname && $parent_content}
            <div class="reply-section">
                <div class="reply-title">{$parent_nickname}回复说:</div>
                <div class="reply-content">
                    {$parent_content}
    </div>
    </div>
    {/if}
            <div class="action-buttons">
                <a href="{$article_url}" class="btn" style="color: white; text-decoration: none;" target="_blank">{$article_title}</a>
                <a href="https://deshengdulikaifa.com" style="color: white; text-decoration: none;" class="btn" target="_blank">访问博客</a>
            </div>
        </div>
        
        <div class="email-footer">
            <p class="footer-text">本邮件由系统自动发送，请勿直接回复</p>
            <p class="footer-brand">德胜独立开发博客</p>
            <p class="footer-powered">Victor Li</p>
    </div>
</div>
</body>
</html> 