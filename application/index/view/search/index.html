<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>搜索文章</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .search-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form .input-group {
            display: flex;
            align-items: stretch;
            box-shadow: 0 2px 12px rgba(0,0,0,0.06);
            border-radius: 32px;
            background: #fff;
            padding: 0;
        }
        .search-form .search-input {
            border: none !important;
            border-radius: 32px 0 0 32px !important;
            padding: 12px 20px !important;
            font-size: 16px !important;
            box-shadow: none !important;
            outline: none !important;
            height: 48px;
            background: transparent;
        }
        .search-form .search-input:focus {
            box-shadow: none !important;
        }
        .search-form .input-group-append {
            display: flex;
        }
        .search-form .search-btn {
            border: none !important;
            border-radius: 0 32px 32px 0 !important;
            background: #1a73e8 !important;
            color: #fff !important;
            font-weight: 600 !important;
            padding: 0 28px !important;
            font-size: 16px !important;
            height: 48px;
            display: flex;
            align-items: center;
            transition: background 0.2s;
        }
        .search-form .search-btn:hover {
            background: #1557b0 !important;
        }
        .search-form .form-control:focus {
            box-shadow: none !important;
        }
        /* 移除input右侧和按钮左侧的间隙 */
        .search-form .search-input {
            margin-right: 0 !important;
        }
        .search-form .search-btn {
            margin-left: 0 !important;
        }
        
        .search-results {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .search-result-item {
            padding: 20px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }
        
        .search-result-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .search-result-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a73e8;
            text-decoration: none;
            margin-bottom: 8px;
            display: block;
        }
        
        .search-result-title:hover {
            color: #1557b0;
            text-decoration: none;
        }
        
        .search-result-content {
            color: #5f6368;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
        }
        
        .search-result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #80868b;
        }
        
        .search-result-category {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .search-result-score {
            background: #34a853;
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 11px;
        }
        
        .search-result-matched {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .matched-badge {
            background: #fff3cd;
            color: #856404;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            border: 1px solid #ffeaa7;
        }
        
        .load-more-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }
        
        .load-more-btn:hover {
            background: #007bff;
            border-color: #007bff;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        .load-more-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .search-stats {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #5f6368;
        }
        
        .no-results i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dadce0;
        }
        
        .search-tips {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .search-tips strong {
            color: #495057;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .loading i {
            font-size: 24px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <!-- 搜索表单 -->
        <div class="search-form">
            <form method="get" action="{:url('search/index')}" id="searchForm">
                <div class="input-group">
                    <input type="text" name="keyword" class="form-control search-input"
                           placeholder="请输入关键词..." value="{$keyword|default=''}" required>
                    <div class="input-group-append">
                        <button class="btn btn-primary search-btn" type="submit">
                            <i class="fa fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- 搜索提示 -->
            {if !$keyword}
            <div class="search-tips">
                <strong>搜索权重：</strong>标题 > 内容 > 分类 > 标签
            </div>
            {/if}
        </div>
        
        <!-- 搜索结果 -->
        {if $keyword}
            {if $total > 0}
                <!-- 搜索统计 -->
                <div class="search-stats">
                    <i class="fa fa-search"></i> 找到 {$total} 条结果
                </div>
                
                <!-- 搜索结果列表 -->
                <div class="search-results" id="searchResultsList">
            {volist name="list" id="item"}
                    <div class="search-result-item">
                        <a href="/detail/{$item.id}" class="search-result-title" target="_blank">
                            {$item.title|htmlentities}
                        </a>
                        <div class="search-result-content">
                            {$item.content_preview|htmlentities}
                        </div>
                        <div class="search-result-meta">
                            <div class="d-flex align-items-center">
                                <span class="search-result-category">
                                    <a href="/category/{$item.category_id}">{$item.category_name|default='未分类'|htmlentities}</a>
                                </span>
                                <span class="search-result-score ml-2">权重: {$item.score}</span>
                            </div>
                            <span>{$item.createtime_text}</span>
                        </div>
                        <div class="search-result-tags mt-1">
                            <i class="fa fa-tags"></i>
                            {volist name="item.tags" id="tag"}
                                <a href="/tag/{$tag.id}" class="badge badge-light">{$tag.name}</a>
                            {/volist}
                        </div>
                        <!-- {if !empty($item.matched_fields)}
                        <div class="search-result-matched">
                            {volist name="item.matched_fields" id="field"}
                            {if $field == 'title'}
                                <span class="matched-badge">标题</span>
                            {elseif $field == 'content'}
                                <span class="matched-badge">内容</span>
                            {elseif $field == 'category'}
                                <span class="matched-badge">分类</span>
                            {elseif $field == 'tag'}
                                <span class="matched-badge">标签</span>
                            {/if}
                            {/volist}
                        </div>
                        {/if} -->
                    </div>
            {/volist}
                </div>
                
                <!-- 加载更多按钮 -->
                {if $hasMore}
                <button class="btn load-more-btn" id="loadMoreBtn" data-page="{$page}" data-keyword="{$keyword}">
                    <i class="fa fa-spinner fa-spin" style="display: none;"></i>
                    加载更多
                </button>
                {/if}
            {else}
                <!-- 无结果提示 -->
                <div class="no-results">
                    <i class="fa fa-search"></i>
                    <h5>没有找到相关内容</h5>
                    <p>请尝试其他关键词</p>
                </div>
            {/if}
        {else}
            <!-- 初始状态 -->
            <div class="no-results">
                <i class="fa fa-search"></i>
                <h5>输入关键词开始搜索</h5>
                <p>支持标题、内容、分类、标签搜索</p>
            </div>
    {/if}
</div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 加载更多功能
            $('#loadMoreBtn').on('click', function() {
                var btn = $(this);
                var currentPage = parseInt(btn.data('page'));
                var keyword = btn.data('keyword');
                var nextPage = currentPage + 1;
                
                // 显示加载状态
                btn.prop('disabled', true);
                btn.find('.fa-spinner').show();
                btn.text('加载中...');
                
                // 发送AJAX请求
                $.get('{:url("search/index")}', {
                    keyword: keyword,
                    page: nextPage,
                    ajax: 1
                }, function(response) {
                    if (response.html) {
                        // 追加新结果
                        $('#searchResultsList').append(response.html);
                        
                        // 更新按钮状态
                        btn.data('page', nextPage);
                        
                        if (!response.hasMore) {
                            btn.remove(); // 没有更多数据，移除按钮
                        } else {
                            btn.prop('disabled', false);
                            btn.find('.fa-spinner').hide();
                            btn.html('<i class="fa fa-spinner fa-spin" style="display: none;"></i>加载更多');
                        }
                    }
                }).fail(function() {
                    alert('加载失败，请重试');
                    btn.prop('disabled', false);
                    btn.find('.fa-spinner').hide();
                    btn.html('<i class="fa fa-spinner fa-spin" style="display: none;"></i>加载更多');
                });
            });
            
            // 表单提交时重置分页
            $('#searchForm').on('submit', function() {
                var keyword = $('input[name="keyword"]').val().trim();
                if (keyword) {
                    // 更新iframe的src，这样会重新加载页面
                    window.parent.document.getElementById('searchIframe').src = 
                        '{:url("search/index")}?keyword=' + encodeURIComponent(keyword);
                }
            });
        });
    </script>
</body>
</html> 