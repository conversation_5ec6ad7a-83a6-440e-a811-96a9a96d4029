<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{$site.description|default='德胜独立开发'}">
    <title>{$title|default='德胜独立开发'}</title>
    {if $site.site_logo}
    <link rel="icon" type="image/png" href="{$site.site_logo|cdnurl}" />
    {else/}
    <link rel="icon" href="{$site.cdnurl}/favicon.ico" />
    {/if}
    <link rel="stylesheet" href="/assets/js/bootstrap-4.6.2-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <!-- <link href="https://fonts.googleapis.com/css?family=Playfair+Display:700,900" rel="stylesheet"> -->
    <link href="{$site.cdnurl}/assets/css/blog.css" rel="stylesheet">
    <link href="{$site.cdnurl}/assets/css/letter-glitch.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css" />
    <script src="/assets/js/falling-flowers.js"></script>
    <script src="/assets/js/splash-cursor.js"></script>
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function () {
                navigator.serviceWorker.register('/sw.js');
                console.log("放假啊司法局")
            });
        }
    </script>
    <style>
        /* body { padding-top: 80px; } */
        /* 移除移动端隐藏aside的样式 */
        .main-layout-container {
            max-width: 1140px;
            margin: 0 auto;
        }

        .main-layout-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        @media (max-width: 991px) {
            .main-layout-container {
                padding: 0 15px;
            }

            .main-layout-row {
                flex-direction: column;
                gap: 24px;
            }
        }

        @media (min-width: 992px) {
            .sidebar-fixed {
                flex: 0 0 280px !important;
                max-width: 280px !important;
                min-width: 280px !important;
            }

            main.order-lg-2 {
                flex: 1 1 0%;
                max-width: calc(100% - 280px - 15px);
                /* Adjusted max-width */
            }
        }

        @media (max-width: 991.98px) {
            .sidebar-fixed {
                display: none !important;
            }
        }
    </style>
    {include file="common/meta" /} 
</head>

<body>
    <canvas id="star-bg-canvas"></canvas>
    {include file="common/global_loading" /}
    {include file="common/header" /}
<style>
    
#searchModal{
    padding: 0 !important;
}
</style>
    <!-- 搜索弹窗 Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel"
        aria-hidden="true" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchModalLabel">搜索文章</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="searchForm" class="mb-3">
                        <div class="input-group">
                            <input type="text" class="form-control search-input" id="searchInput"
                                placeholder="请输入关键词..." autocomplete="off">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary search-btn">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                    </form>
                    <div id="searchResults"></div>
                    <div class="text-center mt-3">
                        <button id="loadMoreBtn" class="btn load-more-btn" style="display:none;">
                            <i class="fa fa-spinner fa-spin" style="display: none;"></i>
                            加载更多
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全站 Banner -->
    <div class="blog-banner text-center py-5 mb-4"
        style="background: url('{$Think.config.site.banner_bg|cdnurl}') center center/cover no-repeat;">
        <img id="banner-avatar" src="{$Think.config.site.site_logo|cdnurl}" class="rounded-circle mb-3 shadow"
            style="width:120px;height:120px;object-fit:cover;border:4px solid #fff;">
        <div class="banner-line mx-auto my-4"></div>
        <h1 class="banner-title mb-4 font-weight-bold">
            <span class="letter-glitch" data-text="{$Think.config.site.name}">
                {$Think.config.site.name}
                <span>{$Think.config.site.name}</span>
                <span>{$Think.config.site.name}</span>
            </span>
        </h1>
        <div class="blog-typing h4 mb-2">
            <span id="typing-text" data-cn="{$Think.config.site.motto_cn|default='热爱技术，专注独立开发'}"
                data-en="{$Think.config.site.motto_en|default='Love coding, focus on indie development'}"></span>
            <span class="typing-cursor">|</span>
            <!-- <span class="letter-glitch-intense" data-text="热爱技术，专注独立开发">
            热爱技术，专注独立开发
        </span> -->
        </div>
    </div>
    <!-- 主体两栏布局 -->
    <div class="main-layout-container">
        <div class="main-layout-row">
            <!-- 主内容区（大屏右，移动端上） -->
            <main class="order-lg-2" style="width: 100%;">
                {block name="content"}{/block}
            </main>
            <!-- 侧栏（大屏左，移动端下，sticky只在大屏生效） -->
            <aside class="order-lg-1 sidebar-fixed">
                {include file="common/sidebar" /}
            </aside>
        </div>
    </div>
    {include file="common/footer" /}
    <style>
        @media (min-width: 992px) {
            .sidebar-fixed {
                flex: 0 0 280px !important;
                max-width: 280px !important;
                min-width: 280px !important;
            }

            .row {
                display: flex;
            }

            main.col-lg-8 {
                flex: 1 1 0%;
                max-width: calc(100% - 280px - 32px);
                /* Adjusted max-width */
            }
        }



        .star-footer p {

            text-align: center;
            margin-bottom: 0;
            font-size: 16px;
            letter-spacing: 0.02em;
            /* 不加 nowrap，允许内容自适应但整体宽度固定 */
        }
    </style>
    <style>
        .star-footer .star-footer-flex {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 12px;
            width: 100%;
            /* max-width: 700px; */
            margin: 0 auto;
            text-align: center;
        }

        .footer-runtime {
            flex: 1 1 100%;
            order: 1;
            min-width: 180px;
            margin-bottom: 0;
        }

        .footer-divider {
            order: 2;
            color: #e0e0f0;
            user-select: none;
        }

        .footer-copyright {
            order: 3;
            color: #e0e0f0;
            min-width: 120px;
            margin-bottom: 0;
        }

        .footer-beian,
        .footer-beian:link,
        .footer-beian:visited {
            color: #e0e0f0 !important;
            text-decoration: none !important;
            transition: color 0.18s;
        }

        .footer-beian:hover,
        .footer-beian:focus {
            color: #fff !important;
            text-decoration: none !important;
        }

        @media (max-width: 600px) {
            .star-footer .star-footer-flex {
                flex-wrap: wrap;
                gap: 6px;
            }

            .footer-runtime {
                order: 1;
                width: 100%;
                margin-bottom: 2px;
            }

            .footer-copyright {
                order: 2;
                width: 50%;
                text-align: center;
            }

            .footer-beian {
                order: 3;
                width: 50%;
                text-align: center;
            }

            .footer-divider {
                display: none;
            }
        }

        @media (max-width: 400px) {
            .star-footer .star-footer-flex {
                flex-direction: column;
                gap: 2px;
            }

            .footer-runtime {
                order: 1;
                width: 100%;
                margin-bottom: 2px;
            }

            .footer-copyright {
                order: 2;
                width: 100%;
                text-align: center;
            }

            .footer-beian {
                order: 3;
                width: 100%;
                text-align: center;
            }
        }
    </style>
    <script>
        function getLifeProgressData() {
            const now = new Date();
            const hours = now.getHours() + now.getMinutes() / 60;
            const dayPercent = Math.round((hours / 24) * 100);
            const weekDay = (now.getDay() + 6) % 7;
            const weekPercent = Math.round(((weekDay + hours / 24) / 7) * 100);
            const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
            const monthPercent = Math.round(((now.getDate() - 1 + hours / 24) / daysInMonth) * 100);
            const start = new Date(now.getFullYear(), 0, 1);
            const diff = now - start;
            const yearPercent = Math.round((diff / (1000 * 60 * 60 * 24 * 365)) * 100);
            const yearMonth = now.getMonth() + 1;

            return [
                {
                    title: `今日已经过去 <span class="life-progress-value">${Math.floor(hours)}</span> 小时`,
                    percent: dayPercent,
                    color: 'blue'
                },
                {
                    title: `这周已经过去 <span class="life-progress-value">${weekDay + 1}</span> 天`,
                    percent: weekPercent,
                    color: 'orange'
                },
                {
                    title: `本月已经过去 <span class="life-progress-value">${now.getDate()}</span> 天`,
                    percent: monthPercent,
                    color: 'red'
                },
                {
                    title: `今年已经过去 <span class="life-progress-value">${yearMonth}</span> 个月`,
                    percent: yearPercent,
                    color: 'green'
                }
            ];
        }

        // 动画函数（宽度+数字）
        function animateBarAndNumber(bar, percentEl, from, to, duration = 1200) {
            const start = performance.now();
            function animate(now) {
                const elapsed = now - start;
                const progress = Math.min(elapsed / duration, 1);
                const value = from + (to - from) * progress;
                bar.style.width = value + '%';
                percentEl.textContent = Math.round(value) + '%';
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    bar.style.width = to + '%';
                    percentEl.textContent = to + '%';
                }
            }
            requestAnimationFrame(animate);
        }

        function renderLifeProgress() {
            const data = getLifeProgressData();
            const list = data.map((item, idx) => `
    <div class="life-progress-item">
      <div class="life-progress-title">
        ${item.title}
        <span class="life-progress-percent" id="life-percent-${idx}">0%</span>
      </div>
      <div class="life-progress-bar-bg">
        <div class="life-progress-bar ${item.color}" id="life-bar-${idx}" style="width:0%"></div>
      </div>
    </div>
  `).join('');
            document.getElementById('life-progress-list').innerHTML = list;

            // 动画显示进度条和数字
            data.forEach((item, idx) => {
                const bar = document.getElementById('life-bar-' + idx);
                const percentEl = document.getElementById('life-percent-' + idx);
                animateBarAndNumber(bar, percentEl, 0, item.percent, 1200 + idx * 200);
            });
        }

        renderLifeProgress();
        // 不再 setInterval
    </script>
    <script src="/assets/js/jquery@3.5.1/jquery.min.js"></script>
    <script src="/assets/js/bootstrap-4.6.2-dist/js/bootstrap.bundle.min.js"></script>
    <script src="{$site.cdnurl}/assets/js/blog.js"></script>
    <script src="{$site.cdnurl}/assets/js/letter-glitch.js"></script>
    {block name="script"}{/block}
    {block name="style"}{/block}
    <script>
        (function () {
            var page = 1;
            var keyword = '';
            var loading = false;
            var lastPage = false;
            function renderResults(list, append) {
                var html = '';
                list.forEach(function (item) {
                    html += '<div class="search-result-item">';
                    html += '<a href="/detail/' + item.id + '" class="search-result-title" style="text-decoration:none;">' + item.title + '</a>';
                    html += '<div class="search-result-content">' + (item.content_preview || '') + '</div>';
                    html += '<div class="search-result-meta">';
                    html += '</div>';
                    html += '</div>';
                });
                if (append) {
                    $('#searchResults').append(html);
                } else {
                    $('#searchResults').html(html);
                }
            }

            function search(reset) {
                if (loading) return;
                loading = true;
                if (reset) {
                    page = 1;
                    lastPage = false;
                    $('#searchResults').html('<div class="text-muted">加载中...</div>');
                }
                $.get('/api/search/index', { keyword: keyword, page: page }, function (res) {
                    loading = false;
                    if (res && res.code === 1) {
                        var list = res.data.list || [];
                        if (reset) {
                            renderResults(list, false);
                        } else {
                            renderResults(list, true);
                        }
                        if (res.data.has_more) {
                            $('#loadMoreBtn').show();
                        } else {
                            $('#loadMoreBtn').hide();
                            if (page > 1 && list.length === 0) {
                                $('#searchResults').append('<div class="text-center text-muted mt-2">没有更多了</div>');
                            }
                        }
                        if (list.length === 0 && page === 1) {
                            $('#searchResults').html('<div class="text-center text-muted">没有找到相关内容</div>');
                        }
                    } else {
                        $('#searchResults').html('<div class="text-danger">搜索失败：' + (res.msg || '未知错误') + '</div>');
                        $('#loadMoreBtn').hide();
                    }
                }).fail(function () {
                    loading = false;
                    $('#searchResults').html('<div class="text-danger">网络错误，请重试</div>');
                    $('#loadMoreBtn').hide();
                });
            }

            $('#searchForm').on('submit', function (e) {
                e.preventDefault();
                keyword = $('#searchInput').val();
                if (!keyword) {
                    $('#searchResults').html('<div class="text-muted">请输入关键词</div>');
                    $('#loadMoreBtn').hide();
                    return;
                }
                search(true);
            });

            $('#loadMoreBtn').on('click', function () {
                if (loading || lastPage) return;
                page++;
                search(false);
            });

            // 每次弹窗打开时重置
            $('#searchModal').on('show.bs.modal', function () {
                $('#searchInput').val('');
                $('#searchResults').html('');
                $('#loadMoreBtn').hide();
                page = 1;
                keyword = '';
                lastPage = false;
            });
        })();
    </script>
    <style>
        /* 搜索弹框风格与首页导航等风格统一 */
        #searchModal .modal-content {
            background: rgba(24,26,38,0.96);
            border-radius: 14px;
            box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25), 0 0 24px 2px #3a3a5a44;
            border: 1.5px solid rgba(255,255,255,0.08);
            color: #e0e0f0;
            /* padding: 0 0 20px 0 !important; */
        }
        #searchModal .modal-header {
            border-bottom: none;
            border-radius: 14px 14px 0 0;
            background: transparent;
            /* padding: 20px 24px 10px 24px; */
        }
        #searchModal .modal-title {
            font-weight: 700;
            font-size: 1.18rem;
            color: #fff;
            letter-spacing: 1px;
        }
        #searchModal .close {
            font-size: 1.4rem;
            color: #aaa;
            opacity: 0.7;
            transition: color 0.2s;
            margin-right: 2px;
        }
        #searchModal .close:hover { color: #7ecfff; opacity: 1; }
        #searchModal .modal-body {
            padding: 0 15px !important;
        }
        #searchForm .input-group {
            border-radius: 10px;
            overflow: hidden;
            background: #23263a;
            box-shadow: 0 2px 8px rgba(0,123,255,0.10);
        }
        #searchInput.form-control {
            border: none;
            border-radius: 0;
            background: #23263a;
            color: #e0e6f0;
            font-size: 1.04rem;
            padding: 0.7rem 1rem;
            box-shadow: none;
            transition: background 0.2s, color 0.2s;
        }
        #searchInput:focus {
            background: #232f4a;
            color: #fff;
            outline: none;
            box-shadow: 0 0 0 2px #7ecfff55;
        }
        .search-btn {
            border: none;
            border-radius: 0 10px 10px 0;
            background: linear-gradient(90deg, #7ecfff 0%, #5a9eff 100%);
            color: #222;
            font-weight: 600;
            padding: 0 1.1rem;
            font-size: 1.04rem;
            transition: background 0.2s, color 0.2s;
        }
        .search-btn:hover {
            background: linear-gradient(90deg, #5a9eff 0%, #7ecfff 100%);
            color: #fff;
        }
        #searchResults {
            min-height: 60px;
        }
        .search-result-item {
            background: #23263a;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.13);
            margin-top: 15px;
            padding: 14px 16px 10px 16px;
            display: flex;
            flex-direction: column;
            border: none;
            transition: box-shadow 0.18s, background 0.18s;
        }
        .search-result-item:hover {
            box-shadow: 0 4px 18px rgba(126,207,255,0.18);
            background: #232f4a;
        }
        .search-result-title {
            font-size: 1.08rem;
            font-weight: 600;
            color: #7ecfff;
            text-decoration: none;
            margin-bottom: 3px;
            position: relative;
            transition: color 0.18s;
            line-height: 1.35;
        }
        .search-result-title::after {
            content: '';
            display: block;
            width: 0;
            height: 2px;
            background: #7ecfff;
            transition: width 0.3s cubic-bezier(.4,0,.2,1);
            position: absolute;
            left: 0;
            bottom: -2px;
        }
        .search-result-title:hover {
            color: #fff;
        }
        .search-result-title:hover::after {
            width: 100%;
        }
        .search-result-content {
            color: #bfc7d5;
            margin: 4px 0 2px 0;
            font-size: 0.97em;
            line-height: 1.6;
        }
        .search-result-meta {
            color: #8ca0c8;
            font-size: 0.91em;
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .search-result-category {
            background: #2d3a5a;
            color: #7ecfff;
            padding: 2px 10px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.97em;
            margin-right: 6px;
        }
        .search-result-score {
            color: #8ca0c8;
            font-size: 0.93em;
            margin-left: 3px;
        }
        .matched-badge {
            display: inline-block;
            background: #ffe082;
            color: #b26a00;
            border-radius: 7px;
            padding: 1.5px 8px;
            font-size: 0.91em;
            margin-right: 5px;
            margin-top: 5px;
        }
        .load-more-btn {
            background: #23263a;
            border: 2px solid #2d3a5a;
            color: #7ecfff;
            padding: 7px 20px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.01em;
            transition: all 0.3s ease;
            margin-top: 8px;
        }
        .load-more-btn:hover {
            background: #7ecfff;
            border-color: #7ecfff;
            color: #222;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(126, 207, 255, 0.18);
        }
        @media (max-width: 700px) {
            #searchModal .modal-dialog {
                margin: 0.5rem;
            }
            #searchModal .modal-content {
                border-radius: 8px;
                padding: 0 0 10px 0 !important;
            }
            #searchModal .modal-body {
                padding: 0 8px !important;
            }
            .search-result-item {
                /* padding: 8px 5px 6px 5px; */
                border-radius: 6px;
            }
            #searchForm .input-group {
                border-radius: 6px;
            }
            .search-btn {
                border-radius: 0 6px 6px 0;
            }
            .search-result-category {
                border-radius: 5px;
                padding: 2px 7px;
            }
            .matched-badge {
                border-radius: 4px;
                padding: 1px 5px;
            }
        }
    </style>
    <script>
        (function () {
            // 隐藏 loading 的函数
            function hideLoading() {
                var loading = document.getElementById('global-loading');
                if (loading) {
                    setTimeout(function () { // 延迟800ms后再渐隐
                        loading.style.opacity = 0;
                        setTimeout(function () {
                            if (loading.parentNode) loading.parentNode.removeChild(loading);
                        }, 400);
                    }, 400); // 这里800可以改成你想要的毫秒数
                }
            }

            window.addEventListener('load', hideLoading);
            // 关键：返回/前进时也要隐藏 loading
            window.addEventListener('pageshow', function (e) {
                hideLoading();
            });

            // 点击页面内链接时，立即显示 loading
            // document.addEventListener('DOMContentLoaded', function () {
            //     console.log("记得哦撒发奖金佛大公鸡哦")
            //     document.body.addEventListener('click', function (e) {
            //         console.log("记得哦撒发奖金佛212大公鸡哦")
            //         var a = e.target.closest('a');
            //         if (
            //             a &&
            //             a.href &&
            //             a.target !== '_blank' &&
            //             !a.href.startsWith('javascript:') &&
            //             !a.href.startsWith('#') &&
            //             !a.hasAttribute('download') &&
            //             a.origin === location.origin
            //         ) {
            //             var loading = document.getElementById('global-loading');
            //             if (!loading) {
            //                 loading = document.createElement('div');
            //                 loading.id = 'global-loading';
            //                 loading.innerHTML = '<div class="loading-spinner"></div><div class="loading-text">正在加载，请稍候...</div>';
            //                 document.body.appendChild(loading);
            //             }
            //             loading.style.opacity = 1;
            //         }
            //     }, true);
            // });
        })();
    </script>
    <script>
        (function () {
            const canvas = document.getElementById('star-bg-canvas');
            const ctx = canvas.getContext('2d');
            let w = window.innerWidth, h = window.innerHeight;
            let stars = [], meteors = [];
            const STAR_NUM = 120;
            const METEOR_MIN = 1, METEOR_MAX = 3; // 流星数量动态变化

            function resize() {
                w = window.innerWidth;
                h = window.innerHeight;
                canvas.width = w;
                canvas.height = h;
            }
            window.addEventListener('resize', resize);
            resize();

            // 动态星星
            function Star() {
                this.x = Math.random() * w;
                this.y = Math.random() * h;
                this.radius = Math.random() * 1.1 + 0.3;
                this.alpha = Math.random() * 0.5 + 0.5;
                this.speed = Math.random() * 0.05 + 0.01;
                this.dir = Math.random() > 0.5 ? 1 : -1;
            }
            Star.prototype.draw = function () {
                ctx.save();
                ctx.globalAlpha = this.alpha;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, 2 * Math.PI);
                ctx.fillStyle = '#fff8c0';
                ctx.shadowColor = '#fff';
                ctx.shadowBlur = 6;
                ctx.fill();
                ctx.restore();
                // 星星缓慢左右漂移
                this.x += this.speed * this.dir;
                if (this.x < 0) this.x = w;
                if (this.x > w) this.x = 0;
                // 闪烁
                this.alpha += (Math.random() - 0.5) * 0.02;
                if (this.alpha < 0.3) this.alpha = 0.3;
                if (this.alpha > 1) this.alpha = 1;
            };

            // 流星
            function Meteor() {
                this.reset();
            }
            Meteor.prototype.reset = function () {
                // 起点在左下方，终点在右上方
                this.x = Math.random() * w * 0.2;
                this.y = h - Math.random() * h * 0.2;
                this.len = Math.random() * 80 + 80;
                this.speed = Math.random() * 8 + 8;
                this.angle = -Math.PI / 4 + (Math.random() - 0.5) * 0.08; // -45度，略有浮动
                this.alpha = 1;
                this.life = 0;
                this.maxLife = Math.random() * 40 + 60;
            };
            Meteor.prototype.draw = function () {
                // 计算流星头尾坐标
                const x2 = this.x + this.len * Math.cos(this.angle);
                const y2 = this.y + this.len * Math.sin(this.angle);

                // 创建渐变
                const grad = ctx.createLinearGradient(x2, y2, this.x, this.y);
                grad.addColorStop(0, 'rgba(255,255,255,1)');
                grad.addColorStop(0.2, 'rgba(255,255,255,0.8)');
                grad.addColorStop(0.7, 'rgba(255,255,255,0.2)');
                grad.addColorStop(1, 'rgba(255,255,255,0)');

                ctx.save();
                ctx.globalAlpha = this.alpha;
                ctx.strokeStyle = grad;
                ctx.shadowColor = '#fff';
                ctx.shadowBlur = 16;
                ctx.beginPath();
                ctx.moveTo(this.x, this.y);
                ctx.lineTo(x2, y2);
                ctx.lineWidth = 2.2;
                ctx.stroke();
                ctx.restore();

                // 头部高亮
                // ctx.save();
                // ctx.globalAlpha = this.alpha;
                // ctx.beginPath();
                // ctx.arc(this.x, this.y, 2.5, 0, 2 * Math.PI);
                // ctx.fillStyle = '#fff';
                // ctx.shadowColor = '#fff';
                // ctx.shadowBlur = 12;
                // ctx.fill();
                // ctx.restore();

                // 运动
                this.x += this.speed * Math.cos(this.angle);
                this.y += this.speed * Math.sin(this.angle);
                this.alpha -= 0.012;
                this.life++;
                // 超出右上边界或透明或寿命到就重置
                if (this.alpha <= 0 || this.x > w || this.y < 0 || this.life > this.maxLife) {
                    this.reset();
                }
            };

            // 初始化
            stars = Array.from({ length: STAR_NUM }, () => new Star());

            // 动态流星生成
            function updateMeteors() {
                // 保持流星数量在范围内
                if (meteors.length < METEOR_MIN || (meteors.length < METEOR_MAX && Math.random() < 0.01)) {
                    meteors.push(new Meteor());
                }
                // 偶尔移除多余的流星
                if (meteors.length > METEOR_MAX && Math.random() < 0.01) {
                    meteors.shift();
                }
            }

            function animate() {
                ctx.clearRect(0, 0, w, h);
                for (let s of stars) s.draw();
                updateMeteors();
                for (let m of meteors) m.draw();
                requestAnimationFrame(animate);
            }
            animate();
        })();
    </script>
    <!-- 列表式右键菜单 -->
    <div id="custom-context-menu" class="custom-context-menu" style="display:none;">
        <ul>
            <div class="context-menu-row">
                <li data-action="back" class="icon-btn"><i class="fa fa-arrow-left"></i></li>
                <li data-action="forward" class="icon-btn"><i class="fa fa-arrow-right"></i></li>
                <li data-action="refresh" class="icon-btn"><i class="fa fa-sync"></i></li>
                <li data-action="top" class="icon-btn"><i class="fa fa-arrow-up"></i></li>
            </div>
            <li data-action="clear-cache"><i class="fa fa-trash"></i> 清空缓存</li>
            <li data-action="random-article"><i class="fa fa-random"></i> 随便逛逛</li>
            <li data-action="random-link"><i class="fa fa-link"></i> 随机友链</li>
            <li data-action="copy-url"><i class="fa fa-copy"></i> 复制地址</li>
        </ul>
    </div>
    <div id="custom-toast" style="display:none;"></div>
    <script>
        // 右键菜单弹窗逻辑（列表式）
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
            const menu = document.getElementById('custom-context-menu');
            menu.style.display = 'block';
            let x = e.clientX, y = e.clientY;
            const w = menu.offsetWidth, h = menu.offsetHeight;
            if (x + w > window.innerWidth) x = window.innerWidth - w - 8;
            if (y + h > window.innerHeight) y = window.innerHeight - h - 8;
            menu.style.left = x + 'px';
            menu.style.top = y + 'px';
        });
        document.addEventListener('mousedown', function (e) {
            const menu = document.getElementById('custom-context-menu');
            if (menu.style.display === 'block' && !menu.contains(e.target)) {
                menu.style.display = 'none';
            }
        });
        document.getElementById('custom-context-menu').addEventListener('click', function (e) {
            if (e.target.classList.contains('divider')) return;
            const li = e.target.closest('li[data-action]');
            if (!li) return;
            const action = li.getAttribute('data-action');
            handleMenuAction(action);
            this.style.display = 'none';
        });
        window.addEventListener('scroll', function () {
            const menu = document.getElementById('custom-context-menu');
            if (menu.style.display === 'block') menu.style.display = 'none';
        });
        function handleMenuAction(action) {
            switch (action) {
                case 'back': history.back(); break;
                case 'forward': history.forward(); break;
                case 'refresh': location.reload(); break;
                case 'top': window.scrollTo({ top: 0, behavior: 'smooth' }); break;
                case 'clear-cache':
                    (async function () {
                        try {
                            // 1. localStorage/sessionStorage
                            localStorage.clear();
                            sessionStorage.clear();

                            // 2. Cookie
                            document.cookie.split(';').forEach(function (c) {
                                var d = window.location.hostname.split('.');
                                while (d.length > 0) {
                                    var cookieBase = encodeURIComponent(c.split('=')[0].trim()) +
                                        '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;domain=' + d.join('.') + ';path=';
                                    var p = location.pathname.split('/');
                                    document.cookie = cookieBase + '/';
                                    while (p.length > 0) {
                                        document.cookie = cookieBase + p.join('/');
                                        p.pop();
                                    }
                                    d.shift();
                                }
                            });

                            // 3. IndexedDB
                            if (window.indexedDB && indexedDB.databases) {
                                const dbs = await indexedDB.databases();
                                for (const db of dbs) {
                                    if (db.name) indexedDB.deleteDatabase(db.name);
                                }
                            } else if (window.indexedDB) {
                                // 兼容旧浏览器：尝试常见库名
                                ['default', 'workbox', 'firebaseLocalStorageDb'].forEach(name => {
                                    try { indexedDB.deleteDatabase(name); } catch (e) { }
                                });
                            }

                            // 4. Service Worker & Cache API
                            if ('serviceWorker' in navigator) {
                                const regs = await navigator.serviceWorker.getRegistrations();
                                for (const reg of regs) await reg.unregister();
                            }
                            if ('caches' in window) {
                                const keys = await caches.keys();
                                for (const key of keys) await caches.delete(key);
                            }
                        } catch (e) { }
                        location.reload();
                    })();
                    break;
                case 'random-article':
                    fetch('/api/article/random')
                        .then(res => res.json())
                        .then(data => {
                            console.log(data, "估计的哦发")
                            // return
                            if (data && data.code === 1 && data.data && data.data.url) {
                                // window.location.href = data.data.url;
                                window.open(data.data.url, '_blank');
                            } else {
                                showToast('暂无可随机访问的文章');
                            }
                        })
                        .catch(() => showToast('网络错误，获取随机文章失败'));
                    break;
                case 'random-link':
                    fetch('/api/links/random')
                        .then(res => res.json())
                        .then(data => {
                            console.log(data, "估计的哦发")
                            if (data && data.code === 1 && data.data && data.data.url) {
                                window.open(data.data.url, '_blank');
                            } else {
                                showToast('暂无可随机访问的友链');
                            }
                        })
                        .catch(() => showToast('网络错误，获取随机友链失败'));
                    break;
                case 'copy-url':
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(location.href).then(() => {
                            showToast('地址已复制');
                        });
                    } else {
                        // 兼容旧浏览器
                        const input = document.createElement('input');
                        input.value = location.href;
                        document.body.appendChild(input);
                        input.select();
                        document.execCommand('copy');
                        document.body.removeChild(input);
                        showToast('地址已复制');
                    }
                    break;
            }
        }
        window.randomArticleUrlList = window.randomArticleUrlList || ["/article/1", "/article/2", "/article/3"];
        window.randomFriendLinkList = window.randomFriendLinkList || ["https://friend1.com", "https://friend2.com"];
    </script>
    <script>
        function showToast(msg) {
            let toast = document.getElementById('custom-toast');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'custom-toast';
                document.body.appendChild(toast);
            }
            toast.textContent = msg;
            toast.style.display = 'block';
            toast.style.position = 'fixed';
            toast.style.left = '50%';
            toast.style.bottom = '60px';
            toast.style.transform = 'translateX(-50%)';
            toast.style.background = 'rgba(24,26,38,0.92)';
            toast.style.color = '#fff';
            toast.style.padding = '12px 28px';
            toast.style.borderRadius = '18px';
            toast.style.fontSize = '1.08rem';
            toast.style.boxShadow = '0 4px 24px rgba(0,0,0,0.13)';
            toast.style.zIndex = 99999;
            toast.style.opacity = 1;
            toast.style.transition = 'opacity 0.4s';
            setTimeout(() => {
                toast.style.opacity = 0;
                setTimeout(() => { toast.style.display = 'none'; }, 400);
            }, 1600);
        }
    </script>
    <style>
        @keyframes split-bounce {
            0% {
                transform: translateY(0);
                opacity: 0.5;
            }

            20% {
                opacity: 1;
                color: #ffc04d;
                text-shadow: 0 2px 8px #fff;
            }

            60% {
                transform: translateY(2px) scale(0.95);
            }

            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .blogger-signature span {
            display: inline-block;
        }
    </style>
    <script>
        // 统一渲染所有人生倒计时卡片
        function getLifeProgressData() {
            const now = new Date();
            const hours = now.getHours() + now.getMinutes() / 60;
            const dayPercent = Math.round((hours / 24) * 100);
            const weekDay = (now.getDay() + 6) % 7;
            const weekPercent = Math.round(((weekDay + hours / 24) / 7) * 100);
            const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
            const monthPercent = Math.round(((now.getDate() - 1 + hours / 24) / daysInMonth) * 100);
            const start = new Date(now.getFullYear(), 0, 1);
            const diff = now - start;
            const yearPercent = Math.round((diff / (1000 * 60 * 60 * 24 * 365)) * 100);
            const yearMonth = now.getMonth() + 1;
            return [
                {
                    title: `今日已经过去 <span class="life-progress-value">${Math.floor(hours)}</span> 小时`,
                    percent: dayPercent,
                    color: 'blue'
                },
                {
                    title: `这周已经过去 <span class="life-progress-value">${weekDay + 1}</span> 天`,
                    percent: weekPercent,
                    color: 'orange'
                },
                {
                    title: `本月已经过去 <span class="life-progress-value">${now.getDate()}</span> 天`,
                    percent: monthPercent,
                    color: 'red'
                },
                {
                    title: `今年已经过去 <span class="life-progress-value">${yearMonth}</span> 个月`,
                    percent: yearPercent,
                    color: 'green'
                }
            ];
        }
        function animateBarAndNumber(bar, percentEl, from, to, duration = 1200) {
            const start = performance.now();
            function animate(now) {
                const elapsed = now - start;
                const progress = Math.min(elapsed / duration, 1);
                const value = from + (to - from) * progress;
                bar.style.width = value + '%';
                percentEl.textContent = Math.round(value) + '%';
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    bar.style.width = to + '%';
                    percentEl.textContent = to + '%';
                }
            }
            requestAnimationFrame(animate);
        }
        function renderLifeProgressAll() {
            document.querySelectorAll('#life-progress-list').forEach(function (el) {
                const data = getLifeProgressData();
                const list = data.map((item, idx) => `
                <div class="life-progress-item">
                  <div class="life-progress-title">
                    ${item.title}
                    <span class="life-progress-percent" id="life-percent-${idx}">0%</span>
                  </div>
                  <div class="life-progress-bar-bg">
                    <div class="life-progress-bar ${item.color}" id="life-bar-${idx}" style="width:0%"></div>
                  </div>
                </div>
            `).join('');
                el.innerHTML = list;
                data.forEach((item, idx) => {
                    const bar = el.querySelector('#life-bar-' + idx);
                    const percentEl = el.querySelector('#life-percent-' + idx);
                    if (bar && percentEl) animateBarAndNumber(bar, percentEl, 0, item.percent, 1200 + idx * 200);
                });
            });
        }
        document.addEventListener('DOMContentLoaded', renderLifeProgressAll);
        // 移动端 Drawer 打开时也触发
        var mobileDrawer = document.getElementById('mobileDrawer');
        if (mobileDrawer) {
            // 打开 Drawer 时禁止 body 滚动
            mobileDrawer.addEventListener('transitionend', function (e) {
                if (
                    e.target.classList.contains('mobile-drawer-content') &&
                    e.propertyName === 'right'
                ) {
                    if (this.classList.contains('open')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                }
            });

            // 保险：关闭 Drawer 时也恢复滚动（防止未触发 transitionend）
            var mask = document.getElementById('mobileDrawerMask');
            if (mask) {
                mask.addEventListener('click', function () {
                    document.body.style.overflow = '';
                });
            }
        }
    </script>
</body>

</html>