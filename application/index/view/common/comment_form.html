{/* 提交留言表单组件 */}
<div class="card mb-4" id="main-comment-form" style="border-radius: 16px;overflow: hidden;margin-bottom: 15px !important;">
    <div class="card-header" style="padding: 0 15px;box-sizing: border-box;height: 45px;display: flex;align-items: center;border-radius: 0 !important;">
        <div class="d-flex justify-content-between align-items-center" style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
            {if isset($form_title)}{$form_title}{else}发表评论{/if}</span>
            <div class="d-flex align-items-center">
                <button type="submit" class="submit-btn" form="{$form_id}">
                    <span class="btn-text">发表留言</span>
                    <span class="btn-loading" style="display: none;">
                        <span class="loading-spinner"></span>
                        发送中...
                    </span>
                </button>
                {if isset($show_close_btn) && $show_close_btn == 'true'}
                <button type="button" class="btn-close-reply btn btn-sm btn-light" style="font-size:1.2rem;height: 28px;margin-left: 15px;line-height: .6rem;margin-top: 0;">×</button>
                {/if}
            </div>
        </div>
    </div>
    <div class="card-body" style="padding: 15px;box-sizing: border-box;">
        <form id="{$form_id}" class="comment-form apply-form" method="post" action="/api/comment/add" style="gap: 15px;">
            <input type="hidden" name="type" value="{if isset($article_id) && $article_id}article{elseif isset($type)}{$type}{else}message{/if}">
            {if isset($article_id) && $article_id}
            <input type="hidden" name="article_id" value="{$article_id}">
            {/if}
            <input type="hidden" name="parent_id" value="{$parent_id|default=0}">
            <input type="hidden" name="reply_to_id" value="{$reply_to_id|default=0}">
            <div class="form-row">
                <div class="form-label">昵称 <span style='color:#ffb400'>*</span></div>
                <input type="text" name="nickname" class="form-input" placeholder="请输入昵称" required>
            </div>
            <div class="form-row">
                <div class="form-label">邮箱 <span style='color:#ffb400'>*</span></div>
                <input type="email" name="email" class="form-input" placeholder="请输入邮箱" required>
            </div>
            <div class="form-row">
                <div class="form-label">网址</div>
                <input type="url" name="website" class="form-input" placeholder="可选，带 http(s)://">
            </div>
            <div class="form-row">
                <textarea name="content" class="form-input" rows="4" placeholder="欢迎留言，分享你的想法..." required></textarea>
            </div>
        </form>
    </div>
</div>

<style>
/* 评论表单组件样式 */
#main-comment-form, .card {
    background: rgba(24,26,38,0.96);
    border-radius: 14px;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 0 12px 1px #3a3a5a33;
    border: 1.5px solid rgba(255,255,255,0.08);
    color: #e0e0f0;
    margin-bottom: 18px;
    position: relative;
}

.comment-form.apply-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
}

.comment-form .form-row {
    display: flex;
    align-items: center;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 0;
}

.comment-form .form-label {
    background: rgba(255,255,255,0.1);
    color: #e0e0f0;
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
    min-width: 80px;
    text-align: center;
    border-right: 1px solid rgba(255,255,255,0.1);
    flex-shrink: 0;
}

.comment-form .form-input, .comment-form textarea.form-input {
    background: rgba(36,38,48,0.98);
    border: none;
    padding: 12px 16px;
    color: #e0e0f0;
    font-size: 14px;
    transition: all 0.2s ease;
    box-sizing: border-box;
    flex: 1;
    min-width: 0;
    border-radius: 0;
}

.comment-form .form-input::placeholder, .comment-form textarea.form-input::placeholder {
    color: #6c757d;
}

.comment-form .form-input:focus, .comment-form textarea.form-input:focus {
    outline: none;
    background: rgba(36,38,48,1);
}

.comment-form textarea.form-input {
    min-height: 80px;
    resize: vertical;
}

.submit-btn {
    background: linear-gradient(135deg, #7ecfff 0%, #5a9eff 100%);
    color: #1a1a1a;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(126, 207, 255, 0.3);
    height: 32px;
    line-height: 1;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(126, 207, 255, 0.4);
    color: #222;
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(26, 26, 26, 0.3);
    border-radius: 50%;
    border-top-color: #1a1a1a;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@media (max-width: 700px) {
    #main-comment-form, .card {
        border-radius: 8px;
    }
    .comment-form .form-row {
        border-radius: 6px;
    }
    .comment-form .form-label {
        padding: 8px 8px;
        font-size: 13px;
    }
    .comment-form .form-input, .comment-form textarea.form-input {
        padding: 8px 8px;
        font-size: 13px;
    }
    .submit-btn {
        height: 28px;
        font-size: 11px;
        padding: 6px 12px;
    }
}
</style>

