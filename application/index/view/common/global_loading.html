<div id="global-loading">
    <div class="star-loading-bg"></div>
    <div class="star-loading-outer">
      <div class="star-loading-planet"></div>
      <div class="star-loading-orbit">
        <div class="star-loading-star star1"></div>
        <div class="star-loading-star star2"></div>
        <div class="star-loading-star star3"></div>
        <div class="star-loading-star star4"></div>
      </div>
      <!-- 动态星点 -->
      <div class="star-dot" style="top:12px;left:18px;animation-delay:0.1s;"></div>
      <div class="star-dot" style="top:60px;left:50px;animation-delay:0.5s;"></div>
      <div class="star-dot" style="top:30px;left:70px;animation-delay:0.3s;"></div>
      <div class="star-dot" style="top:65px;left:20px;animation-delay:0.7s;"></div>
      <div class="star-dot" style="top:10px;left:60px;animation-delay:0.2s;"></div>
      <div class="star-dot" style="top:40px;left:10px;animation-delay:0.6s;"></div>
      <div class="star-dot" style="top:70px;left:75px;animation-delay:0.9s;"></div>
      <div class="star-dot" style="top:55px;left:35px;animation-delay:0.4s;"></div>
    </div>
    <div class="future-brand-glitch">
      <span class="glitch" data-text="VICTOR LI">VICTOR LI</span>
    </div>
    <div class="loading-text">LOADING<br><span>星际量子传输中</span></div>
  </div>
  <style>
  /* @import url('https://fonts.googleapis.com/css?family=Orbitron:700,900&display=swap'); */
  #global-loading {
    position: fixed;
    left: 0; top: 0; right: 0; bottom: 0;
    z-index: 99999;
    background: radial-gradient(ellipse at 50% 40%, #181a26 60%, #0a0c1a 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    pointer-events: all;
    overflow: hidden;
  }
  .star-loading-bg {
    position: absolute;
    left: 0; top: 0; right: 0; bottom: 0;
    z-index: 0;
    pointer-events: none;
    background: 
      radial-gradient(ellipse at 60% 30%, #7ecfff33 0%, #23243a 60%, transparent 100%),
      radial-gradient(ellipse at 30% 70%, #5a9eff22 0%, #23243a 60%, transparent 100%),
      repeating-radial-gradient(circle at 80% 80%, #fff2 0, #fff0 2px, #fff0 40px);
    opacity: 0.95;
    filter: blur(2px);
    animation: future-bg-move 12s linear infinite alternate;
  }
  @keyframes future-bg-move {
    0% { background-position: 60% 30%, 30% 70%, 80% 80%; }
    100% { background-position: 65% 35%, 25% 75%, 85% 85%; }
  }
  .star-loading-outer {
    position: relative;
    width: 90px;
    height: 90px;
    margin-bottom: 18px;
    z-index: 2;
  }
  .star-loading-planet {
    position: absolute;
    left: 50%; top: 50%;
    width: 36px; height: 36px;
    background: radial-gradient(circle at 30% 30%, #7ecfff 70%, #23243a 100%);
    border-radius: 50%;
    box-shadow: 0 0 32px 8px #7ecfffcc, 0 0 0 4px #5a9eff77;
    transform: translate(-50%, -50%);
    z-index: 2;
    animation: planet-glow 2s infinite alternate;
  }
  @keyframes planet-glow {
    0% { box-shadow: 0 0 16px 2px #7ecfff55, 0 0 0 2px #5a9eff33;}
    100% { box-shadow: 0 0 32px 8px #7ecfffcc, 0 0 0 4px #5a9eff77;}
  }
  .star-loading-orbit {
    position: absolute;
    left: 50%; top: 50%;
    width: 68px; height: 68px;
    border-radius: 50%;
    border: 1.5px dashed #7ecfff44;
    transform: translate(-50%, -50%);
    animation: orbit-rotate 2.2s linear infinite;
    z-index: 1;
  }
  @keyframes orbit-rotate {
    100% { transform: translate(-50%, -50%) rotate(360deg);}
  }
  .star-loading-star {
    position: absolute;
    width: 9px; height: 9px;
    background: radial-gradient(circle, #fff 60%, #7ecfff 100%);
    border-radius: 50%;
    box-shadow: 0 0 8px 2px #7ecfff99;
    opacity: 0.85;
    animation: star-twinkle 1.2s infinite alternate;
  }
  .star1 { left: 0; top: 50%; transform: translateY(-50%);}
  .star2 { right: 0; top: 50%; transform: translateY(-50%);}
  .star3 { top: 0; left: 50%; transform: translateX(-50%);}
  .star4 { bottom: 0; left: 50%; transform: translateX(-50%);}
  @keyframes star-twinkle {
    0% { opacity: 0.7; box-shadow: 0 0 4px 1px #7ecfff55;}
    100% { opacity: 1; box-shadow: 0 0 12px 4px #7ecfffcc;}
  }
  .star-dot {
    position: absolute;
    width: 4px; height: 4px;
    background: radial-gradient(circle, #fff 60%, #7ecfff 100%);
    border-radius: 50%;
    opacity: 0.7;
    animation: dot-twinkle 1.8s infinite alternate;
    box-shadow: 0 0 6px 1px #7ecfff55;
  }
  @keyframes dot-twinkle {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
  }
  
  /* 炫酷品牌字 */
  .future-brand-glitch {
    margin-bottom: 8px;
    z-index: 3;
    position: relative;
    display: flex;
    justify-content: center;
  }
  .glitch {
    font-family: 'Orbitron', 'Montserrat', 'Arial', sans-serif;
    font-size: 2.1em;
    font-weight: 900;
    letter-spacing: 0.18em;
    color: #fff;
    position: relative;
    text-shadow:
      0 0 8px #7ecfff,
      0 0 16px #5a9eff,
      0 0 2px #fff,
      0 0 24px #7ecfff99;
    animation: glitch-move 1.8s infinite alternate;
    filter: brightness(1.2) blur(0.1px);
    user-select: none;
  }
  .glitch::before,
  .glitch::after {
    content: attr(data-text);
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    opacity: 0.7;
    z-index: 2;
    pointer-events: none;
  }
  .glitch::before {
    color: #7ecfff;
    text-shadow: 2px 0 8px #7ecfff, 0 0 12px #fff;
    clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
    animation: glitch-top 1.8s infinite linear alternate;
  }
  .glitch::after {
    color: #5a9eff;
    text-shadow: -2px 0 8px #5a9eff, 0 0 12px #fff;
    clip-path: polygon(0 55%, 100% 55%, 100% 100%, 0 100%);
    animation: glitch-bottom 1.8s infinite linear alternate;
  }
  @keyframes glitch-move {
    0% { transform: translateX(0) scale(1);}
    10% { transform: translateX(-1px) scale(1.01);}
    20% { transform: translateX(1px) scale(0.99);}
    30% { transform: translateX(-2px) scale(1.01);}
    40% { transform: translateX(2px) scale(1);}
    50% { transform: translateX(0) scale(1.02);}
    100% { transform: translateX(0) scale(1);}
  }
  @keyframes glitch-top {
    0% { transform: translateY(0);}
    20% { transform: translateY(-2px);}
    40% { transform: translateY(1px);}
    60% { transform: translateY(-1px);}
    80% { transform: translateY(2px);}
    100% { transform: translateY(0);}
  }
  @keyframes glitch-bottom {
    0% { transform: translateY(0);}
    20% { transform: translateY(2px);}
    40% { transform: translateY(-1px);}
    60% { transform: translateY(1px);}
    80% { transform: translateY(-2px);}
    100% { transform: translateY(0);}
  }
  
  .loading-text, .future-loading-text {
    color: #fff;
    font-size: 1.18em;
    font-family: 'Orbitron', 'Montserrat', 'Arial', sans-serif;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 2px 12px #7ecfff99, 0 1px 4px #23243a33;
    z-index: 2;
    text-align: center;
    margin-top: 8px;
    line-height: 1.5;
  }
  .loading-text span {
    display: block;
    font-size: 0.98em;
    font-weight: 400;
    color: #7ecfff;
    letter-spacing: 1.5px;
    margin-top: 2px;
    text-shadow: 0 1px 8px #5a9eff55;
  }
  </style>
  <!-- <link href="https://fonts.googleapis.com/css?family=Orbitron:700,900&display=swap" rel="stylesheet"> -->
  <script>
  window.showGlobalLoading = function() {
    document.getElementById('global-loading').style.display = 'flex';
  };
  window.hideGlobalLoading = function() {
    document.getElementById('global-loading').style.display = 'none';
  };
  </script>