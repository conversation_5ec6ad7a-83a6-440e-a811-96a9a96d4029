{if condition="$total > 0"}
<nav aria-label="分页" class="custom-paginator-nav">
    <ul class="custom-pagination">
        {if condition="$page > 1"}
        <li>
            <a class="custom-page-link" style="text-decoration: none;" href="{$urlss|default=''}?page={$page-1}{$query|default=''}">上一页</a>
        </li>
        {else/}
        <li><span class="custom-page-link disabled">上一页</span></li>
        {/if}
        {for start="1" end="ceil($total/$limit)+1" name="i"}
        <li>
            <a class="custom-page-link {if condition="$i == $page"}active{/if}" style="text-decoration: none;" href="{$urlss|default=''}?page={$i}{$query|default=''}">{$i}</a>
        </li>
        {/for}
        {if condition="$page < ceil($total/$limit)"}
        <li>
            <a class="custom-page-link" style="text-decoration: none;" href="{$urlss|default=''}?page={$page+1}{$query|default=''}">下一页</a>
        </li>
        {else/}
        <li><span class="custom-page-link disabled">下一页</span></li>
        {/if}
    </ul>
</nav>
<style>
.custom-paginator-nav {
  /* margin: 32px 0 0 0; */
}
.custom-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 0;
  margin: 0;
  list-style: none;
}
.custom-page-link {
  display: inline-block;
  min-width: 36px;
  padding: 6px 14px;
  background: rgba(36,38,48,0.98);
  color: #b2bac0;
  border-radius: 8px;
  border: none;
  font-size: 1em;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
  cursor: pointer;
  user-select: none;
}
.custom-page-link:hover:not(.active):not(.disabled) {
  background: #e0e0f0;
  color: #23242b;
}
.custom-page-link.active {
  background: #e0e0f0;
  color: #23242b;
  font-weight: 700;
  box-shadow: 0 2px 8px 0 rgba(255,255,255,0.10);
  cursor: default;
}
.custom-page-link.disabled {
  background: rgba(36,38,48,0.5);
  color: #6c757d;
  cursor: not-allowed;
  font-weight: 400;
}
</style>
{/if} 