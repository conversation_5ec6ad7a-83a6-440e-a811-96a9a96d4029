<div class="{if $item.parent_id != 0}child-commentssdsaf{/if}"></div>
<style>
    .child-commentssdsaf {
        flex-grow: 1;
        margin-left: 70px;
        padding-top: 15px;
        border-top: 1px dashed #6c757d;
    }
</style>

<div class="comment-header">
    {if isset($item.is_admin) && $item.is_admin}
    <!-- 博主头像 - 使用博主信息样式 -->
    <div class="comment-admin-avatar-badge"
        style="display: flex;align-items: center;justify-content: center;width: 60px;height: 60px;">
        <img src="{if isset($item.avatar_url)}{$item.avatar_url}{else}/assets/img/default-avatar.jpeg{/if}"
            style="width: 50px;height: 50px;position: absolute;z-index: 0;border-radius: 50%;margin-left: -5px;margin-top: -5px;"
            alt="{if isset($item.nickname)}{$item.nickname}{else}博主{/if}"
            onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
        <img src="/assets/img/honor-light.png" class="comment-admin-avatar-badge-img" alt="徽章" />
    </div>
    {else/}
    <!-- 普通用户头像 -->
    <img src="{if isset($item.avatar_url)}{$item.avatar_url}{else}/assets/img/default-avatar.jpeg{/if}"
        class="comment-avatar" alt="{if isset($item.nickname)}{$item.nickname}{else}用户{/if}"
        onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
    {/if}
    <div class="comment-user-info">
        <div class="comment-meta">
            <span>
                <span class="comment-nickname">
                    {if !empty($item.website)}
                    <a href="{$item.website}" target="_blank" class="text-decoration-none">{if
                        isset($item.nickname)}{$item.nickname}{else}用户{/if}</a>
                    {else/}
                    {if isset($item.nickname)}{$item.nickname}{else}用户{/if}
                    {/if}
                    <span class="comment-time">{if
                        isset($item.updatetime_text)}{$item.updatetime_text}{else}{/if}</span>
                </span>
            </span>
            <div class="comment-actions">
                <button type="button" class="btn-like" data-id="{if isset($item.id)}{$item.id}{else}0{/if}">
                    <i class="fa fa-heart"></i> 点赞 <span>{if isset($item.likes)}{$item.likes}{else}0{/if}</span>
                </button>
                <button type="button" class="btn-reply" data-id="{if isset($item.id)}{$item.id}{else}0{/if}">
                    <i class="fa fa-reply"></i> 回复
                </button>
            </div>
        </div>
        <div class="comment-details">
            {if !empty($item.ip_location)}
            <span class="comment-detail"><i class="fa fa-map-marker-alt"></i> {$item.ip_location}</span>
            {/if}
            {if !empty($item.device)}
            <span class="comment-detail"><i class="fa fa-mobile-alt"></i> {$item.device}</span>
            {/if}
            {if !empty($item.browser)}
            <span class="comment-detail"><i class="fa fa-globe"></i> {$item.browser}</span>
            {/if}
        </div>
    </div>
</div>
<div class="comment-content">
    {if $item.reply_to_id != 0}
    <div class="reply-quote">
        回复 {if !empty($item.reply_to_website)}
        <a href="{$item.reply_to_website}" class="reply-nickname" style="text-decoration: none;" target="_blank"
            rel="noopener noreferrer">
            {$item.reply_to_nickname}
        </a>
        {elseif !empty($item.reply_to_email)}
        <a href="mailto:{$item.reply_to_email}" class="reply-nickname" style="text-decoration: none;">
            {$item.reply_to_nickname}
        </a>
        {else/}
        <span class="reply-nickname">
            {$item.reply_to_nickname}
        </span>
        {/if}：
        <span class="reply-content">{$item.reply_to_content|mb_substr=0,60}</span>
    </div>
    {/if}
    <div class="main-content">
        {if isset($item.content_formatted)}{$item.content_formatted}{/if}
    </div>
</div>
<style>
    .comment-content {
        margin-top: 6px;
        font-size: 1rem;
        color: #e0e6f0;
        line-height: 1.7;
        word-break: break-all;
    }

    .reply-quote {
        background: rgba(40, 50, 80, 0.7);
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
        font-size: 0.95em;
        color: #7ecfff;
        box-shadow: 0 2px 8px 0 rgba(30, 40, 80, 0.08);
        position: relative;
    }

    .reply-quote .reply-nickname {
        color: #ffb400;
        font-weight: 600;
        margin-right: 2px;
    }

    .reply-quote .reply-content {
        color: #b3e5fc;
        margin-left: 4px;
        /* font-style: italic; */
    }

    .main-content {
        color: #e0e6f0;
        font-size: 1.05em;
        margin-top: 0px;
    }
</style>
<!-- 回复表单 -->
<div style="margin-left: 75px;display: none;" class="reply-form-container" id="reply-form-{$item.id}">
    {assign name="show_close_btn" value="true"}
    {if $form_id == 'zipinglun'}
    {assign name="parent_id" value="$item.parent_id"}
    {assign name="reply_to_id" value="$item.id"}
    {else/}
    {assign name="parent_id" value="$item.id"}
    {assign name="reply_to_id" value="0"}
    {/if}
    {assign name="form_id" value="$form_id . '-' . $item.id"}
    {assign name="article_id" value="$item.article_id"}
    {/* 确保传递article_id */}
    {include file="common/comment_form"/}
</div>