<div class="article-list-container">
  {empty name="articleList"}
  <div class="empty-articles">暂无文章。</div>
  {else/}
  {volist name="articleList" id="article" key="idx"}
  <div class="article-card-box">
    <div class="article-cover-wrapper">
      <div class="article-cover" style="position:relative;height: 100%;">
        {if $article.is_recommend == 1 || $article.is_recommend == '1'}
        <span class="top-badge-simple">推荐</span>
        {/if}
        <img src="{$article.image}" alt="封面" class="cover-img" />
      </div>
    </div>
    <div class="article-info">
      <div class="article-info-header">
        <a class="article-title-link" style="text-decoration: none;" href="/detail/{$article.id}">
          <span class="article-title">{$article.title}</span>
        </a>
      </div>
      <a class="article-summary-link" style="text-decoration: none;" href="/detail/{$article.id}">
        <div class="article-summary">{$article.summary|default=''} </div>
      </a>
      <div class="article-info-footer">
        <div style="display: flex;align-items: center;gap: 5px;">
          <span class="meta">发表于{$article.createtime|date='Y-m-d',###}</span>
          <span class="meta">/</span>
          <span class="meta" style="gap: 2px;"><span>阅读</span>{$article.views|default=0}</span>
        </div>
        <a class="meta category-meta" style="text-decoration: none;" href="/categoryList/{$article.category_id}">
          <i class="fa fa-folder-open"></i> {$article.category_name|default='未分类'}
        </a>
      </div>
    </div>
  </div>
  {/volist}
  {/empty}
  {include file="common/paginator" total=$total limit=$limit page=$page urlss=$urlss query=$query /}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.article-card-box').forEach(function(card) {
    var articleId = card.querySelector('.article-title-link')?.getAttribute('href')?.replace('/detail/', '');
    if (!articleId) return;
    card.addEventListener('click', function(e) {
      // 如果点击的是标题、摘要、分类，阻止冒泡
      if (
        e.target.closest('.article-title-link') ||
        e.target.closest('.article-summary-link') ||
        e.target.closest('.category-meta')
      ) {
        return;
      }
      window.location.href = '/detail/' + articleId;
    });
  });
});
</script>
<style>
.article-list-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.article-card-box {
  display: flex;
  background: #23242b;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.13);
  border: 1.5px solid rgba(255,255,255,0.07);
  overflow: hidden;
  align-items: stretch;
  min-height: 120px;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: card-fadein 0.5s cubic-bezier(.4,1.6,.6,1) forwards;
}
@keyframes card-fadein {
  to {
    opacity: 1;
    transform: none;
  }
}
.article-card-box:hover {
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18),0 0 12px 1px #3a3a5a33;
  transform: translateY(-3px) scale(1.01);
  transition: box-shadow 0.2s, transform 0.3s;
}
.article-cover-wrapper {
  overflow: hidden;
  width: 210px;
  height: 140px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.article-cover {
  width: 100%;
  height: 100%;
  position: relative;
}
.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  display: block;
  transition: transform 0.35s cubic-bezier(.4,1.6,.6,1), box-shadow 0.2s;
}
.article-card-box:hover .cover-img {
  transform: scale(1.08);
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
}
.top-badge-simple {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  background: rgba(0,0,0,0.4);
  color: #b2bac0;
  font-size: .8em;
  border-radius: 4px;
  padding: 2px 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 2px;
  border: none;
  box-shadow: none;
}
.article-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
  background: transparent;
  position: relative;
}
.article-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
	/* // 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。 */
	display: -webkit-box;
	/* // 设置或检索伸缩盒对象的子元素的排列方式 。 */
	-webkit-box-orient:vertical;
	/* // 用省略号“…”隐藏超出范围的文本 。 */
	text-overflow: ellipsis;
	overflow: hidden;
	/* 用来限制在一个块元素显示的文本的行数。 */
	-webkit-line-clamp:1;
	line-clamp:1;
}
.article-title-link {
  color: inherit;
  text-decoration: none;
  display: inline-block;
  max-width: 100%;
}
.article-title-link:hover .article-title {
  color: #e0e0f0;
  text-decoration: none;
}
.article-title {
  font-size: 1em;
  font-weight: bold;
  color: #e0e0f0;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;/* 新增 */
  transition: color 0.2s;
  flex: 1;        /* 新增，确保在flex布局下可收缩 */
}
.article-summary-link {
  color: inherit;
  text-decoration: none;
  display: block;
}
.article-summary-link:hover .article-summary {
  color: #e0e0f0;
  text-decoration: none;
}
.article-summary {
  color: #6c757d;
  font-size: 0.95em;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  transition: color 0.2s;
}
.article-info-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 18px;
  font-size: 0.9em;
  color: #a1a1aa;
  margin-top: auto;
}
.meta {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #6c757d;
}
.category-meta {
  font-weight: 500;
  transition: color 0.2s;
  cursor: pointer;
  text-decoration: none;
}
.category-meta:hover {
  color: #e0e0f0;
  text-decoration: none;
}
.empty-articles {
  text-align: center;
  color: #6c757d;
  font-size: 15px;
  padding: 40px 20px;
}
@media (max-width: 700px) {
  .article-summary-link {
    display: none !important;
  }
  .article-list-container {
    max-width: 100%;
    padding: 0 4px;
    gap: 15px;
  }
  .article-card-box {
    min-height: unset;
    border-radius: 10px;
    flex-direction: row;
    padding: 0;
    opacity: 0;
    transform: translateY(30px);
    animation: card-fadein 0.5s cubic-bezier(.4,1.6,.6,1) forwards;
  }
  .article-card-box:hover {
    transform: translateY(-2px) scale(1.01);
  }
  .article-cover-wrapper {
    width: 90px;
    height: 90px;
    min-width: 120px;
    max-width: 120px;
    border-radius: 10px 0 0 10px;
  }
  .article-cover {
    border-radius: 10px 0 0 10px;
  }
  .cover-img {
    border-radius: 10px 0 0 10px;
    transition: transform 0.35s cubic-bezier(.4,1.6,.6,1), box-shadow 0.2s;
  }
  .article-card-box:hover .cover-img {
    transform: scale(1.06);
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  }
  .article-info {
    padding: 10px 8px 8px 8px;
  }
}
</style> 