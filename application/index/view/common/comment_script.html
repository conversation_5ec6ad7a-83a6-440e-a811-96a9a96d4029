{/* 评论JavaScript组件 */}
<script src="/assets/js/jquery@3.5.1/jquery.min.js"></script>
<script>
    // 评论组件配置
    window.commentConfig = {
        type: "{if isset($type)}{$type}{else}message{/if}",
        articleId: "{if isset($article_id)}{$article_id}{else}{/if}",
        currentSort: '{if isset($sort)}{$sort}{else}desc{/if}',
        currentPage: '{if isset($page)}{$page}{else}1{/if}',
        pageSize: '{if isset($limit)}{$limit}{else}10{/if}',
        totalItems: '{if isset($total)}{$total}{else}0{/if}'
    };

    // 工具函数：字符串转颜色
    function stringToColor(str) {
        var hash = 0;
        for (var i = 0; i < str.length; i++) {
            hash = str.charCodeAt(i) + ((hash << 5) - hash);
        }
        var color = '#';
        for (var i = 0; i < 3; i++) {
            var value = (hash >> (i * 8)) & 0xFF;
            color += ('00' + value.toString(16)).substr(-2);
        }
        return color;
    }

    // 工具函数：获取头像URL
    function getAvatarUrl(email, nickname) {
        email = email.trim().toLowerCase();
        var qqMatch = email.match(/^([1-9][0-9]{4,10})@qq\.com$/);
        if (qqMatch) {
            var qq = qqMatch[1];
            return 'https://q1.qlogo.cn/g?b=qq&nk=' + qq + '&s=100';
        }
        var letter = (nickname || email).charAt(0).toUpperCase();
        var color = stringToColor(email);
        var svg = `<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="24" fill="${color}"/><text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" font-family="Arial,Helvetica,sans-serif" dy=".1em">${letter}</text></svg>`;
        return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
    }

    $(function () {
        // 加载缓存的用户信息
        function loadCachedUserInfo() {
            var cachedNickname = localStorage.getItem('message_nickname');
            var cachedEmail = localStorage.getItem('message_email');
            var cachedWebsite = localStorage.getItem('message_website');
            if (cachedNickname) {
                $('input[name="nickname"]').val(cachedNickname);
            }
            if (cachedEmail) {
                $('input[name="email"]').val(cachedEmail);
            }
            if (cachedWebsite) {
                $('input[name="website"]').val(cachedWebsite);
            }
        }

        // 保存用户信息到缓存
        function saveUserInfoToCache(nickname, email, website) {
            if (nickname && nickname.trim()) {
                localStorage.setItem('message_nickname', nickname.trim());
            }
            if (email && email.trim()) {
                localStorage.setItem('message_email', email.trim());
            }
            if (website && website.trim()) {
                localStorage.setItem('message_website', website.trim());
            }
        }

        // 初始化点赞按钮状态
        function initLikeButtons() {
            var likedComments = JSON.parse(localStorage.getItem('likedComments') || '[]');
            $('.btn-like').each(function () {
                var commentId = parseInt($(this).data('id'));
                if (likedComments.includes(commentId)) {
                    $(this).addClass('liked');
                }
            });
        }

        // 页面加载时初始化
        loadCachedUserInfo();
        initLikeButtons();

        // 绑定回复按钮事件
        function bindReplyEvents() {
            $('.btn-reply').off('click').on('click', function () {
                var commentId = $(this).data('id');
                // 隐藏所有回复表单
                $('.reply-form-container').hide();
                // 隐藏主表单（如果你希望只显示一个表单）
                $('#main-comment-form').hide();
                // 显示当前这条评论的回复表单
                var replyForm = $('#reply-form-' + commentId);
                replyForm.show();
                // 自动回显昵称、邮箱、网址
                var cachedNickname = localStorage.getItem('message_nickname');
                var cachedEmail = localStorage.getItem('message_email');
                var cachedWebsite = localStorage.getItem('message_website');
                if (cachedNickname) replyForm.find('input[name="nickname"]').val(cachedNickname);
                if (cachedEmail) replyForm.find('input[name="email"]').val(cachedEmail);
                if (cachedWebsite) replyForm.find('input[name="website"]').val(cachedWebsite);
                bindFormSubmitEvents()
            });

            // 关闭按钮逻辑
            $(document).off('click', '.btn-close-reply').on('click', '.btn-close-reply', function () {
                $(this).closest('.reply-form-container').hide();
                // 关闭后显示主表单
                $('#main-comment-form').show();
                bindFormSubmitEvents()
            });
        }

        // 绑定点赞事件
        function bindLikeEvents() {
            $('.btn-like').off('click').on('click', function () {
                var $btn = $(this);
                var commentId = $btn.data('id');
                var $count = $btn.find('span');
                var currentCount = parseInt($count.text()) || 0;

                if ($btn.hasClass('liked')) {
                    // 取消点赞
                    // $.post('/api/comment/unlike', { comment_id: commentId }, function (res) {
                    //     if (res.code === 1) {
                    //         $btn.removeClass('liked');
                    //         $count.text(currentCount - 1);
                    //         var likedComments = JSON.parse(localStorage.getItem('likedComments') || '[]');
                    //         var index = likedComments.indexOf(commentId);
                    //         if (index > -1) {
                    //             likedComments.splice(index, 1);
                    //             localStorage.setItem('likedComments', JSON.stringify(likedComments));
                    //         }
                    //     } else {
                    //         alert(res.msg || '操作失败');
                    //     }
                    // });
                } else {
                    // 点赞
                    $.post('/api/comment/like', { comment_id: commentId }, function (res) {
                        if (res.code === 1) {
                            $btn.addClass('liked');
                            $count.text(currentCount + 1);
                            var likedComments = JSON.parse(localStorage.getItem('likedComments') || '[]');
                            if (!likedComments.includes(commentId)) {
                                likedComments.push(commentId);
                                localStorage.setItem('likedComments', JSON.stringify(likedComments));
                            }
                        } else {
                            alert(res.msg || '操作失败');
                        }
                    });
                }
            });
        }

        // 绑定排序按钮事件
        function bindSortEvents() {
            // 只需在页面初始化时绑定一次
            $(document).off('click', '.sort-btn').on('click', '.sort-btn', function () {
                var sort = $(this).data('sort');
        // 更新全局配置
        window.commentConfig.currentSort = sort;
        // 排序时回到第一页
        window.commentConfig.currentPage = 1;
        // 更新URL参数（可选，方便刷新后保留状态）
        var url = new URL(window.location);
        url.searchParams.set('sort', sort);
        url.searchParams.set('page', 1);
        window.history.replaceState({}, '', url);

        // AJAX刷新评论列表
        refreshCommentList(0,1); // 0 表示主评论，回第一页
            });
        }

        // 绑定加载更多事件
        function bindLoadMoreEvents() {
            $('#loadMoreBtn').off('click').on('click', function () {
                var $btn = $(this);
                var nextPage = parseInt($btn.data('page')) + 1;
                $btn.prop('disabled', true);
                $btn.find('.fa-spinner').show();

                $.get(window.location.pathname, {
                    sort: window.commentConfig.currentSort,
                    page: nextPage,
                    ajax: 1,
                    type: window.commentConfig.type,
                    article_id: window.commentConfig.articleId
                }, function (response) {
                    $btn.prop('disabled', false);
                    $btn.find('.fa-spinner').hide();
                    if (response.commentList && response.commentList.length > 0) {
                        // 这里需要后端返回HTML片段
                        $('.comment-list').append(response.html);
                        window.commentConfig.currentPage = nextPage;
                        $btn.data('page', nextPage);
                        bindReplyEvents();
                        bindLikeEvents();
                        initLikeButtons();

                        // 更新加载更多按钮状态
                        if (nextPage >= Math.ceil(window.commentConfig.totalItems / window.commentConfig.pageSize)) {
                            $('.load-more-container').hide();
                        }
                    } else {
                        $('.load-more-container').hide();
                    }
                }).fail(function () {
                    $btn.prop('disabled', false);
                    $btn.find('.fa-spinner').hide();
                    alert('加载失败，请重试');
                });
            });
        }

        // 绑定加载更多子评论事件
        function bindLoadMoreChildrenEvents() {
            $(document).off('click', '.btn-load-more-children').on('click', '.btn-load-more-children', function () {
                var $btn = $(this);
                var $btnContainer = $(this).closest('.load-more-children');
                var parentId = $btn.closest('.load-more-children').data('parent-id');
                var currentPage = parseInt($btn.closest('.load-more-children').attr('data-page'));
                var total = parseInt($btn.closest('.load-more-children').data('total'));
                var nextPage = currentPage + 1;

                $btn.prop('disabled', true).find('.fa-spinner').show();

                $.get('/index/comment/children', {
                    parent_id: parentId,
                    page: nextPage,
                    type: window.commentConfig.type,
                    article_id: window.commentConfig.articleId
                }, function (response) {
                    $btn.prop('disabled', false).find('.fa-spinner').hide();
                    if (response.code == 1 && response.data.html && response.data.html) {
                        console.log("数组，数据", response.data.html)
                        // $('#children-' + parent_id).before(response.data.html);
                        $('#children-' + parentId).append(response.data.html);
                        // 更新当前页码
                        // $btnContainer.data('page', nextPage);
                        $btnContainer.attr('data-page', nextPage);

                        // 更新 data-total
                        $btnContainer.attr('data-total', response.data.total);

                        // 更新按钮文本
                        if (response.data.total > 0) {
                            $btn.html(
                                '<i class="fa fa-spinner fa-spin me-2" style="display:none"></i>' +
                                '查看剩余 ' + response.data.total + ' 条回复'
                            );
                        } else {
                            $btnContainer.remove(); // 没有更多了，移除按钮
                        }

                        // 重新绑定事件
                        bindReplyEvents();
                        bindLikeEvents();
                        initLikeButtons();
                    } else {
                        // $btn.closest('.load-more-children').remove();
                        // 加载失败或没有更多内容
                        $btnContainer.remove();
                    }
                }).fail(function () {
                    $btn.prop('disabled', false).find('.fa-spinner').hide();
                    alert('加载失败，请重试');
                });
            });
        }

        // 绑定表单提交事件
        function bindFormSubmitEvents() {
            $(document).off('submit', '.comment-form').on('submit', '.comment-form', function (e) {
                e.preventDefault();

                var $form = $(this);
                // 如果表单不可见，直接忽略
                if (!$form.is(':visible')) {
                    return;
                }

                // 查找提交按钮（支持两种按钮结构）
                var $submitBtn = $form.find('button[type="submit"]');
                if ($submitBtn.length === 0) {
                    // 如果表单内没有提交按钮，查找外部按钮
                    var formId = $form.attr('id');
                    $submitBtn = $('button[form="' + formId + '"]');
                }

                // 设置按钮加载状态
                function setButtonLoading(loading) {
                    var $btnText = $submitBtn.find('.btn-text');
                    var $btnLoading = $submitBtn.find('.btn-loading');

                    if ($btnText.length > 0 && $btnLoading.length > 0) {
                        // 新样式按钮（带加载动画）
                        if (loading) {
                            $submitBtn.prop('disabled', true);
                            $btnText.hide();
                            $btnLoading.show().css('display', 'inline-flex');
                        } else {
                            $submitBtn.prop('disabled', false);
                            $btnText.show();
                            $btnLoading.hide();
                        }
                    } else {
                        // 旧样式按钮（简单文本）
                        if (loading) {
                            $submitBtn.prop('disabled', true).text('提交中...');
                        } else {
                            $submitBtn.prop('disabled', false).text('发表留言');
                        }
                    }
                }

                setButtonLoading(true);

                $.ajax({
                    url: $form.attr('action'),
                    method: 'POST',
                    data: $form.serialize(),
                    success: function (response) {
                        if (response.code == 1) {
                            // 保存用户信息到缓存
                            var nickname = $form.find('input[name="nickname"]').val();
                            var email = $form.find('input[name="email"]').val();
                            var website = $form.find('input[name="website"]').val();
                            saveUserInfoToCache(nickname, email, website);

                            // 强制用最新缓存更新所有表单的回显
                            loadCachedUserInfo();
                            var parentId = $form.find('input[name="parent_id"]').val();
                            // 只清空留言内容
                            $form.find('textarea[name="content"]').val('');
                            // 隐藏回复表单
                            $form.closest('.reply-form-container').hide();
                            $('#main-comment-form').show();
                            // 自定义成功提示
                            if (typeof showToast === 'function') {
                                showToast('留言提交成功');
                            }
                            // ajax刷新评论列表
                            refreshCommentList(parentId);
                        } else {
                            if (typeof showToast === 'function') {
                                showToast(response.msg || '提交失败，请重试');
                            } else {
                                alert(response.msg || '提交失败，请重试');
                            }
                        }
                    },
                    error: function () {
                        if (typeof showToast === 'function') {
                            showToast('提交失败，请重试');
                        } else {
                            alert('提交失败，请重试');
                        }
                    },
                    complete: function () {
                        setButtonLoading(false);
                    }
                });
            });
        }

        // 刷新评论列表（ajax）
        function refreshCommentList(parentId,objs) {
            console.log(parentId)
            console.log(window.commentConfig.currentPage)
            var page = 1;
            var sort = "desc";

            if (parentId && parentId != "0") {
                page = window.commentConfig.currentPage;
                sort = window.commentConfig.currentSort;
            }else{
                 // 更新URL
                var url = new URL(window.location);
                if(objs===1){
                    sort = window.commentConfig.currentSort;
                }
                url.searchParams.set('page', 1);
                url.searchParams.set('sort', sort);
                window.history.replaceState({}, '', url);
            }


            var params = {
                sort: sort,
                page: page,
                ajax: 1,
                type: window.commentConfig.type,
                article_id: window.commentConfig.articleId
            };
            
            // 打印当前页面路径，帮助调试
            console.log("当前页面路径:", window.location.pathname);
            
            $.get(window.location.pathname, params, function (response) {
                console.log("收到评论列表响应:", response);
                
                if (response && response.html) {
                    // 检查是否在文章详情页
                    var isDetailPage = window.location.pathname.includes('/detail/');
                    console.log("是否是文章详情页:", isDetailPage);
                    
                    if (isDetailPage) {
                        // 文章详情页 - 直接替换评论容器内容
                        console.log("刷新文章详情页评论区");
                        var detailCommentContainer = $('.card-body').find('.comment-list').closest('.card');
                        if (detailCommentContainer.length > 0) {
                            detailCommentContainer.replaceWith(response.html);
                        } else {
                            // 如果找不到正确的容器，尝试其他选择器
                            $('.card-body').filter(function() {
                                return $(this).find('.comment-list').length > 0;
                            }).html(response.html);
                        }
                    } else {
                        // 留言页面 - 替换整个卡片
                        console.log("刷新留言页评论区");
                        $('.card:has(.comment-list)').replaceWith(response.html);
                    }
                    
                    // 重新绑定事件
                    bindReplyEvents();
                    bindLikeEvents();
                    initLikeButtons();
                }
            }).fail(function(error) {
                console.error("评论刷新失败:", error);
            });
        }

        // 初始化所有事件绑定
        bindReplyEvents();
        bindLikeEvents();
        bindSortEvents();
        bindLoadMoreEvents();
        bindLoadMoreChildrenEvents();
        bindFormSubmitEvents();
    });
</script>