<div class="blogger-card blogger-profile-card" style="display: flex;flex-direction: column;align-items: center;">
    <div class="blogger-banner-bg" style="background-image:url('/assets/img/GIPHY.gif');filter: brightness(0.5);background-size: cover;background-position: center  80%; /* 重点：对齐底部 */"></div>
    <div class="blogger-avatar-badge">
        <img src="{$site.blogger_avatar|cdnurl}" class="blogger-avatar-img" alt="博主头像">
        <img src="/assets/img/honor-light.png" class="blogger-avatar-badge-img" alt="徽章" />
    </div>
    <div class="blogger-nickname-row">
        <span class="blogger-nickname-gradient"><PERSON></span>
    </div>
    <div class="blogger-signature mb-2">{$site.blogger_signature|default=''}</div>
    <div class="blogger-stats-row">
        <div class="blogger-stat">
            <div class="stat-num">{$site.article_count}</div>
            <div class="stat-label">文章数</div>
        </div>
        <div class="blogger-stat">
            <div class="stat-num">{$site.category_count}</div>
            <div class="stat-label">分类数</div>
        </div>
        <div class="blogger-stat">
            <div class="stat-num">{$site.tag_count}</div>
            <div class="stat-label">标签数</div>
        </div>
    </div>
    <div class="blogger-social-row">
        <a href="https://github.com/FDSGS" target="_blank">
            <svg viewBox="0 0 1024 1024" fill="#DDDDDD" version="1.1" xmlns="http://www.w3.org/2000/svg"
                width="22" height="22">
                <path
                    d="M512 95.872a426.666667 426.666667 0 0 0-134.912 831.445333c21.333333 3.754667 29.312-9.045333 29.312-20.266666 0-10.112-0.512-43.733333-0.512-79.445334-107.221333 19.712-134.954667-26.154667-143.488-50.133333a155.136 155.136 0 0 0-43.733333-60.288c-14.933333-7.978667-36.266667-27.733333-0.554667-28.245333a85.376 85.376 0 0 1 65.621333 43.733333 91.178667 91.178667 0 0 0 124.245334 35.2 89.770667 89.770667 0 0 1 27.221333-57.088c-94.933333-10.666667-194.133333-47.445333-194.133333-210.645333a166.058667 166.058667 0 0 1 43.733333-114.688 153.344 153.344 0 0 1 4.266667-113.066667s35.712-11.178667 117.333333 43.733333a402.218667 402.218667 0 0 1 213.333333 0c81.578667-55.466667 117.333333-43.733333 117.333334-43.733333a153.301333 153.301333 0 0 1 4.266666 113.066667 165.077333 165.077333 0 0 1 43.733334 114.688c0 163.712-99.754667 199.978667-194.688 210.645333a101.034667 101.034667 0 0 1 28.8 78.933333c0 57.088-0.512 102.954667-0.512 117.333334 0 11.221333 7.978667 24.533333 29.312 20.266666A426.88 426.88 0 0 0 512 95.872z"
                    fill="var(--title)"></path>
            </svg>
        </a>
        <a href="https://www.xiaohongshu.com/user/profile/5a08082a11be1033a70a338f?xsec_token=YBUzTyaa6ABFv4EWb3ruV6CKdxCDm1RJCcomv1Sy-P63Q=&xsec_source=app_share&xhsshare=CopyLink&appuid=5a08082a11be1033a70a338f&apptime=1753022020&share_id=055b0722251d4146a3ae1106eeb6f5d1" target="_blank">
            <svg t="1752742547935" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="64611" width="22" height="22">
                <path
                    d="M760.71522 641.464264c-156.422105 107.858946-525.625115 245.170496-583.495098 180.899367-57.884258-64.286982 133.591468-440.941607 240.900228-561.389518 71.519087-80.29966 124.026728-127.577752 157.522922-141.834276l342.59487 380.49015c-0.764007 22.648641-53.271648 69.926733-157.538776 141.848551z"
                    fill="#FF2942" p-id="64612"
                    data-spm-anchor-id="a313x.search_index.0.i27.31723a81vtQr8E" class="selected">
                </path>
                <path
                    d="M916.573498 501.12884a256 106.666667 48 1 0-342.59487-380.490151 256 106.666667 48 1 0 342.59487 380.490151Z"
                    fill="#F9B01C" p-id="64613"></path>
            </svg>
        </a>
        <a href="https://mp.weixin.qq.com/s/Cx6U4WegKWrng_qkkdl0vA" target="_blank">
            <svg t="1752800797734" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="1483" width="22" height="22">
                <path
                    d="M980.032 612.768c0-127.968-128.032-232.192-271.776-232.192-152.256 0-272.16 104.32-272.16 232.192 0 128.128 119.904 232.16 272.16 232.16 31.904 0 64.064-8.064 96.064-16l87.712 48-24.096-79.936c64.256-48.192 112.096-112.096 112.096-184.224z m-355.968-35.52a36.256 36.256 0 1 1 0-72.48 36.256 36.256 0 0 1 0 72.48z m176.096-0.288a36.256 36.256 0 1 1-0.032-72.48 36.256 36.256 0 0 1 0 72.48z"
                    fill="#02C161" p-id="1484"></path>
                <path
                    d="M395.968 140.512c-175.904 0-320 119.904-320 272.16 0 87.904 47.936 160.032 128.032 216.032l-32 96.256 111.872-56.096c40.032 7.872 72.128 16.032 112.096 16.032 10.048 0 20.032-0.448 29.888-1.28A238.144 238.144 0 0 1 416 616.576c0-139.84 120.064-253.312 272.032-253.312 10.432 0 20.672 0.736 30.816 1.92-27.712-128.864-165.568-224.64-322.88-224.64z m-107.52 219.584a43.52 43.52 0 1 1 0.064-87.04 43.52 43.52 0 0 1-0.032 87.04z m223.52 0a43.52 43.52 0 1 1 0-87.04 43.52 43.52 0 0 1 0 87.04z"
                    fill="#02C161" p-id="1485"></path>
            </svg>
            <!-- <svg t="1752800621201" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17810" width="22" height="22"><path d="M118.438 395.821c2.369-6.149 33.678 183.946 178.428 278.972l-15.978 88.06c40.236 1.303 78.704-16.536 103.663-48.072l-0.086-0.086c84.842 23.606 196.817 17.2 343.246-47.298 0 0-43.24 143.184-125.283 194.781-152.07 95.37-391.61 50.738-483.258-107.495-26.056-44.976-68.305-170.702-0.732-358.862z m624.476-100.788c148.088 53.771 229.99 212.072 188.204 363.764-14.73 49.792-44.489 149.376-227.525 229.61-9.82 4.386 293.331-368.45-161.674-588.472a367.37 367.37 0 0 1 200.995-4.902z m49.484-62.69c6.89 8.34-416.676-105.991-518.918 322.485v-0.215l-0.1-0.113c-3.226-3.672-83.855-96.422-80.479-191.444C211.493 203.351 345.881 82.208 506.905 80c52.068 0 156.248 0 285.493 152.342z" fill="#02C161" p-id="17811"></path></svg> -->
        </a>

        <a href="https://space.bilibili.com/332526375" target="_blank">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="22"
                height="22">
                <path
                    d="M777.514667 131.669333a53.333333 53.333333 0 0 1 0 75.434667L728.746667 255.829333h49.92A160 160 0 0 1 938.666667 415.872v320a160 160 0 0 1-160 160H245.333333A160 160 0 0 1 85.333333 735.872v-320a160 160 0 0 1 160-160h49.749334L246.4 207.146667a53.333333 53.333333 0 1 1 75.392-75.434667l113.152 113.152c3.370667 3.370667 6.186667 7.04 8.448 10.965333h137.088c2.261333-3.925333 5.12-7.68 8.490667-11.008l113.109333-113.152a53.333333 53.333333 0 0 1 75.434667 0z m1.152 231.253334H245.333333a53.333333 53.333333 0 0 0-53.205333 49.365333l-0.128 4.010667v320c0 28.117333 21.76 51.157333 49.365333 53.162666l3.968 0.170667h533.333334a53.333333 53.333333 0 0 0 53.205333-49.365333l0.128-3.968v-320c0-29.44-23.893333-53.333333-53.333333-53.333334z m-426.666667 106.666666c29.44 0 53.333333 23.893333 53.333333 53.333334v53.333333a53.333333 53.333333 0 1 1-106.666666 0v-53.333333c0-29.44 23.893333-53.333333 53.333333-53.333334z m320 0c29.44 0 53.333333 23.893333 53.333333 53.333334v53.333333a53.333333 53.333333 0 1 1-106.666666 0v-53.333333c0-29.44 23.893333-53.333333 53.333333-53.333334z"
                    fill="#fb7299"></path>
            </svg>
        </a>
        <a href="https://wpa.qq.com/msgrd?v=3&uin=1316737309&site=qq&menu=yes" target="_blank">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20"
                height="22">
                <path
                    d="M511.09761 957.257c-80.159 0-153.737-25.019-201.11-62.386-24.057 6.702-54.831 17.489-74.252 30.864-16.617 11.439-14.546 23.106-11.55 27.816 13.15 20.689 225.583 13.211 286.912 6.767v-3.061z"
                    fill="#FAAD08"></path>
                <path
                    d="M496.65061 957.257c80.157 0 153.737-25.019 201.11-62.386 24.057 6.702 54.83 17.489 74.253 30.864 16.616 11.439 14.543 23.106 11.55 27.816-13.15 20.689-225.584 13.211-286.914 6.767v-3.061z"
                    fill="#FAAD08"></path>
                <path
                    d="M497.12861 474.524c131.934-0.876 237.669-25.783 273.497-35.34 8.541-2.28 13.11-6.364 13.11-6.364 0.03-1.172 0.542-20.952 0.542-31.155C784.27761 229.833 701.12561 57.173 496.64061 57.162 292.15661 57.173 209.00061 229.832 209.00061 401.665c0 10.203 0.516 29.983 0.547 31.155 0 0 3.717 3.821 10.529 5.67 33.078 8.98 140.803 35.139 276.08 36.034h0.972z"
                    fill="#000000"></path>
                <path
                    d="M860.28261 619.782c-8.12-26.086-19.204-56.506-30.427-85.72 0 0-6.456-0.795-9.718 0.148-100.71 29.205-222.773 47.818-315.792 46.695h-0.962C410.88561 582.017 289.65061 563.617 189.27961 534.698 185.44461 533.595 177.87261 534.063 177.87261 534.063 166.64961 563.276 155.56661 593.696 147.44761 619.782 108.72961 744.168 121.27261 795.644 130.82461 796.798c20.496 2.474 79.78-93.637 79.78-93.637 0 97.66 88.324 247.617 290.576 248.996a718.01 718.01 0 0 1 5.367 0C708.80161 950.778 797.12261 800.822 797.12261 703.162c0 0 59.284 96.111 79.783 93.637 9.55-1.154 22.093-52.63-16.623-177.017"
                    fill="#000000"></path>
                <path
                    d="M434.38261 316.917c-27.9 1.24-51.745-30.106-53.24-69.956-1.518-39.877 19.858-73.207 47.764-74.454 27.875-1.224 51.703 30.109 53.218 69.974 1.527 39.877-19.853 73.2-47.742 74.436m206.67-69.956c-1.494 39.85-25.34 71.194-53.24 69.956-27.888-1.238-49.269-34.559-47.742-74.435 1.513-39.868 25.341-71.201 53.216-69.974 27.909 1.247 49.285 34.576 47.767 74.453"
                    fill="#FFFFFF"></path>
                <path
                    d="M683.94261 368.627c-7.323-17.609-81.062-37.227-172.353-37.227h-0.98c-91.29 0-165.031 19.618-172.352 37.227a6.244 6.244 0 0 0-0.535 2.505c0 1.269 0.393 2.414 1.006 3.386 6.168 9.765 88.054 58.018 171.882 58.018h0.98c83.827 0 165.71-48.25 171.881-58.016a6.352 6.352 0 0 0 1.002-3.395c0-0.897-0.2-1.736-0.531-2.498"
                    fill="#FAAD08"></path>
                <path
                    d="M467.63161 256.377c1.26 15.886-7.377 30-19.266 31.542-11.907 1.544-22.569-10.083-23.836-25.978-1.243-15.895 7.381-30.008 19.25-31.538 11.927-1.549 22.607 10.088 23.852 25.974m73.097 7.935c2.533-4.118 19.827-25.77 55.62-17.886 9.401 2.07 13.75 5.116 14.668 6.316 1.355 1.77 1.726 4.29 0.352 7.684-2.722 6.725-8.338 6.542-11.454 5.226-2.01-0.85-26.94-15.889-49.905 6.553-1.579 1.545-4.405 2.074-7.085 0.242-2.678-1.834-3.786-5.553-2.196-8.135"
                    fill="#000000"></path>
                <path
                    d="M504.33261 584.495h-0.967c-63.568 0.752-140.646-7.504-215.286-21.92-6.391 36.262-10.25 81.838-6.936 136.196 8.37 137.384 91.62 223.736 220.118 224.996H506.48461c128.498-1.26 211.748-87.612 220.12-224.996 3.314-54.362-0.547-99.938-6.94-136.203-74.654 14.423-151.745 22.684-215.332 21.927"
                    fill="#FFFFFF"></path>
                <path
                    d="M323.27461 577.016v137.468s64.957 12.705 130.031 3.91V591.59c-41.225-2.262-85.688-7.304-130.031-14.574"
                    fill="#EB1C26"></path>
                <path
                    d="M788.09761 432.536s-121.98 40.387-283.743 41.539h-0.962c-161.497-1.147-283.328-41.401-283.744-41.539l-40.854 106.952c102.186 32.31 228.837 53.135 324.598 51.926l0.96-0.002c95.768 1.216 222.4-19.61 324.6-51.924l-40.855-106.952z"
                    fill="#EB1C26"></path>
            </svg>
        </a>
        <a href="mailto:<EMAIL>" target="_blank">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="21"
                height="22">
                <path
                    d="M512 938.666667C276.352 938.666667 85.333333 747.648 85.333333 512S276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667z m341.333333-426.666667a341.333333 341.333333 0 1 0-169.301333 294.869333l-43.008-73.685333A256 256 0 1 1 768 512v42.666667a42.666667 42.666667 0 0 1-85.333333 0V384h-57.770667a170.666667 170.666667 0 1 0 2.816 253.44A128 128 0 0 0 853.333333 554.666667v-42.666667z m-341.333333-85.333333a85.333333 85.333333 0 1 1 0 170.666666 85.333333 85.333333 0 0 1 0-170.666666z"
                    fill="#dc4835"></path>
            </svg>
        </a>
    </div>
</div>
<style>
    .blogger-profile-card {
        background: rgba(24, 26, 38, 0.96);
        border-radius: 22px;
        box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18), 0 0 12px 1px #3a3a5a33;
        border: 1.5px solid rgba(255, 255, 255, 0.08);
        color: #e0e0f0;
        padding: 0 0 18px 0;
        margin-bottom: 15px;
        overflow: hidden;
        position: relative;
        text-align: center;
    }

    .blogger-banner-bg {
        width: 100%;
        height: 120px;
        background-size: cover;
        background-position: center;
        /* border-radius: 22px 22px 0 0; */
        position: relative;
    }

    .blogger-banner-bg::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: -4px;
        /* 适当下移，保证模糊溢出 */
        height: 8px;
        /* 模糊高度 */
        background: inherit;
        filter: blur(4px);
        overflow: hidden;
        z-index: 2;
        pointer-events: none;
    }

    .blogger-avatar-badge {
        position: relative;
        margin-top: -68px;
        margin-bottom: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .blogger-avatar-img {
        width: 88px;
        height: 88px;
        border-radius: 50%;
        border: 4px solid #fff;
        box-shadow: 0 2px 12px #0005;
        background: #fff;
        z-index: 2;
        top: 0;
    }

    .blogger-avatar-badge-img {
        position: absolute;
        width: 110px;
        height: 110px;
        left: 50%;
        top: 0;
        transform: translate(-50%, -10%);
        z-index: 999;
        pointer-events: none;
    }

    .blogger-nickname-row {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        margin-top: 2px;
    }

    .blogger-nickname-gradient {
        font-size: 1.3em;
        font-weight: bold;
        background: linear-gradient(90deg, #7ecfff, #b388ff, #fff176, #ff9800);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        filter: drop-shadow(0 0 6px #7ecfff88);
    }

    .blogger-level {
        background: #222d;
        color: #b6ffb6;
        font-size: 0.95em;
        font-weight: 600;
        border-radius: 6px;
        padding: 2px 8px;
        margin-left: 2px;
        letter-spacing: 1px;
    }

    .blogger-signature {
        color: #bdbdbd;
        font-size: 0.95em;
        margin-bottom: 8px;
    }

    .blogger-stats-row {
        display: flex;
        width: calc(100% - 30px);
        justify-content: space-around;
        align-items: center;
        margin: 8px 0 8px 0;
        border-bottom: 1px solid #4445;
        padding-bottom: 8px;
    }

    .blogger-stat {
        flex: 1;
        position: relative;
        text-align: center;
    }

    .blogger-stat:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 25%;
        height: 50%;
        width: 1px;
        background: #4445;
        transform: translateX(50%);
    }

    .blogger-stat .stat-num {
        font-size: 1.4em;
        font-weight: bold;
        color: #fff;
    }

    .blogger-stat .stat-label {
        font-size: 0.88em;
        color: #bdbdbd;
        margin-top: 2px;
    }

    .blogger-social-row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: calc(100% - 30px);
    }

    .blogger-social-row a {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.18s;
    }

    .blogger-social-row img,
    .blogger-social-row svg {
        width: 22px;
        height: 22px;
        object-fit: contain;
        transition: transform 0.18s;
    }

    .blogger-social-row a:hover img,
    .blogger-social-row a:hover svg {
        transform: scale(1.18);
    }
</style>
<!-- 公告通知卡片 -->
<div class="blogger-card">
    <div
        style="font-size: 1em;display: flex;align-items: center;width: 100%;border-bottom:1px solid rgba(255,255,255,0.08);height: 45px;padding: 0 15px;">
        <svg t="1752819154500" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="53237" width="22" height="22"><path d="M189.31 408.87m120 0l404.05 0q120 0 120 120l0 134.26q0 120-120 120l-404.05 0q-120 0-120-120l0-134.26q0-120 120-120Z" fill="#E9F1FF" p-id="53238"></path><path d="M808 900H216c-83.81 0-152-68.19-152-152V444c0-83.81 68.19-152 152-152h592c83.81 0 152 68.19 152 152v304c0 83.81-68.19 152-152 152zM216 356a88.1 88.1 0 0 0-88 88v304a88.1 88.1 0 0 0 88 88h592a88.1 88.1 0 0 0 88-88V444a88.1 88.1 0 0 0-88-88z" fill="#2C5CCB" p-id="53239"></path><path d="M756 353.12a31.9 31.9 0 0 1-19.37-6.55L517.22 179.45a7.91 7.91 0 0 0-9.57 0L288.28 346.57a32 32 0 0 1-38.78-50.91l219.36-167.12a72.08 72.08 0 0 1 87.15 0l219.36 167.12A32 32 0 0 1 756 353.12z" fill="#2C5CCB" p-id="53240"></path><path d="M320.01 607.96m-40 0a40 40 0 1 0 80 0 40 40 0 1 0-80 0Z" fill="#2C5CCB" p-id="53241"></path><path d="M512.05 607.96m-40 0a40 40 0 1 0 80 0 40 40 0 1 0-80 0Z" fill="#2C5CCB" p-id="53242"></path><path d="M704.09 607.96m-40 0a40 40 0 1 0 80 0 40 40 0 1 0-80 0Z" fill="#2C5CCB" p-id="53243"></path></svg>
        <div style="margin-left: 4px;">公告</div>
</div>
    <div class="blogger-notice-contentsss"
        style="padding: 15px;width: 100%;box-sizing: border-box;line-height:inherit;">
        {$site.blogger_notice}
    </div>
</div>
<style>
    .blogger-notice-title {
        font-size: 1em;
        display: flex;
        align-items: center;
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        height: 45px;
        padding: 0 15px;
    }

    .notice-svg-icon {
        width: 22px;
        height: 22px;
        vertical-align: middle;
        flex-shrink: 0;
    }

    .blogger-notice-divider {
        height: 1.5px;
        background: #4445;
        margin-bottom: 12px;
        border-radius: 1px;
    }

    .blogger-notice-contentsss {
        font-size: 0.9em;
        color: #6c757d;
    }

    .blogger-notice-contentsss p {
        line-height: inherit;
        margin: inherit;
    }
</style>
<!-- 人生倒计时卡片 -->
<div class="blogger-card">
    <div
        style="font-size: 1em;display: flex;align-items: center;width: 100%;border-bottom:1px solid rgba(255,255,255,0.08);height: 45px;padding: 0 15px;">
        <svg t="1752803075011" class="icon" viewBox="0 0 1024 1024" version="1.1"
            xmlns="http://www.w3.org/2000/svg" p-id="15161" width="24" height="24">
            <path
                d="M849.408 251.050667a41.301333 41.301333 0 0 0-58.026667 0l-86.869333 86.869333 58.026667 58.026667 86.869333-86.869334a41.301333 41.301333 0 0 0 0-58.026666z"
                fill="#D8E3F0" p-id="15162"></path>
            <path
                d="M860.842667 238.933333a51.2 51.2 0 0 0-72.362667 0l-5.290667 5.290667 72.362667 72.362667 5.290667-5.290667a51.2 51.2 0 0 0 0-72.362667z"
                fill="#FCFEFF" p-id="15163"></path>
            <path d="M470.869333 159.061333h81.92v65.536h-81.92z" fill="#D8E3F0" p-id="15164"></path>
            <path d="M421.717333 85.333333l180.224 0 0 73.728-180.224 0 0-73.728Z" fill="#FCFEFF"
                p-id="15165"></path>
            <path
                d="M585.557333 85.333333H529.066667a17.066667 17.066667 0 0 1 17.066666 17.066667v40.96a17.066667 17.066667 0 0 1-17.066666 17.066667h56.32a17.066667 17.066667 0 0 0 17.066666-17.066667V102.4a17.066667 17.066667 0 0 0-16.896-17.066667z"
                fill="#D8E3F0" p-id="15166"></path>
            <path
                d="M511.829333 580.437333m-358.229333 0a358.229333 358.229333 0 1 0 716.458667 0 358.229333 358.229333 0 1 0-716.458667 0Z"
                fill="#FFC670" p-id="15167"></path>
            <path
                d="M512 704.853333a902.144 902.144 0 0 1-354.133333-72.021333 358.4 358.4 0 0 0 708.096 0A904.533333 904.533333 0 0 1 512 704.853333z"
                fill="#FFA742" p-id="15168"></path>
            <path
                d="M511.829333 580.437333m-286.549333 0a286.549333 286.549333 0 1 0 573.098667 0 286.549333 286.549333 0 1 0-573.098667 0Z"
                fill="#FCFEFF" p-id="15169"></path>
            <path
                d="M511.829333 580.437333m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z"
                fill="#D8E3F0" p-id="15170"></path>
            <path
                d="M552.448 539.818667l-81.066667 81.066666L341.333333 450.56a28.501333 28.501333 0 0 1 2.901334-37.034667 28.501333 28.501333 0 0 1 37.205333-2.901333z"
                fill="#F6716F" p-id="15171"></path>
            <path
                d="M511.829333 580.437333m-73.728 0a73.728 73.728 0 1 0 147.456 0 73.728 73.728 0 1 0-147.456 0Z"
                fill="#D8E3F0" p-id="15172"></path>
            <path
                d="M511.829333 580.437333m-36.352 0a36.352 36.352 0 1 0 72.704 0 36.352 36.352 0 1 0-72.704 0Z"
                fill="#FCFEFF" p-id="15173"></path>
            <path
                d="M512 403.456a17.066667 17.066667 0 0 0 17.066667-17.066667v-29.866666a17.066667 17.066667 0 0 0-34.133334 0v29.866666a17.066667 17.066667 0 0 0 17.066667 17.066667zM386.730667 729.6a17.066667 17.066667 0 0 0 0-24.064 17.066667 17.066667 0 0 0-24.234667 0L341.333333 726.698667A17.066667 17.066667 0 0 0 365.568 750.933333zM334.848 580.266667a17.066667 17.066667 0 0 0-17.066667-17.066667h-29.866666a17.066667 17.066667 0 1 0 0 34.133333h29.866666a17.066667 17.066667 0 0 0 17.066667-17.066666zM512 757.418667a17.066667 17.066667 0 0 0-17.066667 17.066666v29.866667a17.066667 17.066667 0 0 0 34.133334 0v-29.866667a17.066667 17.066667 0 0 0-17.066667-17.066666zM661.162667 705.536a17.066667 17.066667 0 0 0-24.064 0 17.066667 17.066667 0 0 0 0 24.064L658.090667 750.933333A17.066667 17.066667 0 0 0 682.666667 750.933333a17.066667 17.066667 0 0 0 0-24.064zM658.090667 409.6l-20.992 21.162667a17.066667 17.066667 0 0 0 0 24.064 17.066667 17.066667 0 0 0 24.064 0L682.666667 434.176A17.066667 17.066667 0 0 0 658.090667 409.6z"
                fill="#3D3D63" p-id="15174"></path>
            <path
                d="M849.408 333.141333a17.066667 17.066667 0 0 0 18.261333-3.754666l5.290667-5.461334a68.266667 68.266667 0 1 0-96.426667-96.426666l-5.461333 5.290666a17.066667 17.066667 0 0 0-3.072 18.261334l-29.866667 29.866666a374.442667 374.442667 0 0 0-167.594666-71.338666v-34.133334h15.701333a34.133333 34.133333 0 0 0 34.133333-34.133333V102.4a34.133333 34.133333 0 0 0-34.133333-34.133333h-148.138667a34.133333 34.133333 0 0 0-34.133333 34.133333v40.96a34.133333 34.133333 0 0 0 34.133333 34.133333h15.701334v34.133334A375.466667 375.466667 0 1 0 819.2 364.202667z m0-81.578666a34.133333 34.133333 0 0 1 5.802667 40.618666l-46.933334-46.421333a34.133333 34.133333 0 0 1 40.618667 5.802667z m-58.538667 24.746666l34.133334 34.133334-26.965334 26.965333a330.581333 330.581333 0 0 0-34.133333-34.133333zM438.784 142.677333V102.4h146.773333v39.594667z m96.938667 34.133334v29.696c-7.850667 0-15.872-0.853333-23.893334-0.853334s-15.872 0-23.893333 0.853334v-30.378667zM512 921.6a341.333333 341.333333 0 1 1 341.333333-341.333333 341.333333 341.333333 0 0 1-341.333333 341.333333z"
                fill="#3D3D63" p-id="15175"></path>
            <path
                d="M512 276.650667A303.786667 303.786667 0 1 0 815.616 580.266667 304.128 304.128 0 0 0 512 276.650667z m0 573.269333A269.653333 269.653333 0 1 1 781.482667 580.266667 269.653333 269.653333 0 0 1 512 849.92z"
                fill="#3D3D63" p-id="15176"></path>
            <path
                d="M602.624 580.266667a90.453333 90.453333 0 0 0-87.722667-90.453334L392.533333 396.8A45.738667 45.738667 0 0 0 328.533333 460.8l92.842667 122.368A90.624 90.624 0 0 0 602.624 580.266667z m-246.784-139.946667a11.434667 11.434667 0 0 1 1.194667-15.018667 11.264 11.264 0 0 1 14.677333-1.194666l99.498667 75.434666a91.136 91.136 0 0 0-40.106667 40.106667z m99.328 139.946667A56.661333 56.661333 0 1 1 512 637.098667 56.661333 56.661333 0 0 1 455.168 580.266667zM688.810667 580.266667a17.066667 17.066667 0 0 0 17.066666 17.066666h29.866667a17.066667 17.066667 0 0 0 0-34.133333h-29.866667a17.066667 17.066667 0 0 0-17.066666 17.066667z"
                fill="#3D3D63" p-id="15177"></path>
        </svg>
        <div style="margin-left: 4px;">人生倒计时</div>
    </div>
    <div id="life-progress-list" style="padding: 15px;width: 100%;box-sizing: border-box;"></div>
</div>
<style>
    .life-progress-item {
        margin-bottom: 15px;
    }

    .life-progress-title {
        font-size: 0.9em;
        color: #6c757d;
        margin-bottom: 2px;
    }

    .life-progress-value {
        color: #ff5722;
        font-weight: bold;
        font-size: 1.1em;
    }

    .life-progress-bar-bg {
        background: #414243;
        border-radius: 5px;
        height: 10px;
        position: relative;
        overflow: hidden;
    }

    .life-progress-bar {
        height: 100%;
        border-radius: 5px;
        position: absolute;
        left: 0;
        top: 0;
        animation: stripe-move 1.2s linear infinite;
    }

    .life-progress-bar.blue {
        background: repeating-linear-gradient(45deg, #4fc3f7, #4fc3f7 7px, #81d4fa 7px, #81d4fa 14px);
        background-size: 20px 14px;
    }

    .life-progress-bar.orange {
        background: repeating-linear-gradient(45deg, #ffc107, #ffc107 7px, #ffe082 7px, #ffe082 14px);
        background-size: 20px 14px;
    }

    .life-progress-bar.red {
        background: repeating-linear-gradient(45deg, #ff8a80, #ff8a80 7px, #ff5252 7px, #ff5252 14px);
        background-size: 20px 14px;
    }

    .life-progress-bar.green {
        background: repeating-linear-gradient(45deg, #43a047, #43a047 7px, #66bb6a 7px, #66bb6a 14px);
        background-size: 20px 14px;
    }

    .life-progress-percent {
        float: right;
        font-size: 0.9em;
        color: #6c757d;
        font-weight: bold;
        margin-top: 2px;
        min-width: 38px;
        text-align: right;
        letter-spacing: 1px;
    }

    @keyframes stripe-move {
        0% {
            background-position: 0 0;
        }

        100% {
            background-position: 20px 0;
        }
    }
</style>

<!-- 微信公众号 Flip Card -->
<!-- <div class="blogger-card mt-3 p-0" style="background:none;box-shadow:none;">
    <div class="flip-card-wechat" style="width:100%;height:180px;position:relative;">
        <div class="flip-card-inner-wechat"
            style="width:100%;height:100%;transition:transform 0.6s;transform-style:preserve-3d;position:relative;">
            <div class="flip-card-front-wechat"
                style="background:#07C160;color:#fff;border-radius:28px;position:absolute;width:100%;height:100%;backface-visibility:hidden;display:flex;flex-direction:column;justify-content:center;align-items:flex-start;padding:32px 32px 24px 32px;overflow:hidden;">
                <h2 style="font-weight:900;font-size:2.1em;line-height:1;">微信公众号 <span
                        style="font-size:0.8em;">✨</span></h2>
                <h3 style="margin-top:18px;font-size:1.25em;font-weight:600;">快人一步获取最新文章</h3>
                <div
                    style="position:absolute;left:0;top:0;width:100%;height:100%;background:url('/uploads/wechat-public-cover.webp') right bottom no-repeat;background-size:160px auto;opacity:1;border-radius:28px;z-index:0;">
                </div>
            </div>
            <div class="flip-card-back-wechat"
                style="background:#07C160;color:#fff;border-radius:28px;position:absolute;width:100%;height:100%;backface-visibility:hidden;transform:rotateY(180deg);display:flex;flex-direction:column;justify-content:center;align-items:center;padding:32px 32px 24px 32px;">
                <span style="font-size:2.1em;font-weight:900;line-height:1;">扫一扫</span>
                <span style="margin-top:18px;font-size:1.25em;font-weight:600;">不错过精彩文章</span>
                <img src="/uploads/qrcode_for_gh_444cd0fafc0b_258.jpg" alt="social card"
                    style="width:90px;height:90px;margin-top:18px;border-radius:12px;box-shadow:0 2px 12px rgba(0,0,0,0.10);">
            </div>
        </div>
    </div>
</div> -->
<style>
    /* Flip Card Hover 动画 */
    .flip-card-wechat {
        perspective: 800px;
        min-height: 180px;
    }

    .flip-card-inner-wechat {
        transition: transform 0.6s cubic-bezier(.4, 2, .6, 1);
        transform-style: preserve-3d;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .flip-card-wechat:hover .flip-card-inner-wechat {
        transform: rotateY(180deg);
    }

    .flip-card-front-wechat,
    .flip-card-back-wechat {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 28px;
        box-sizing: border-box;
        backface-visibility: hidden;
        overflow: hidden;
    }

    .flip-card-back-wechat {
        transform: rotateY(180deg);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
</style>
<!-- 热门分类卡片 -->
<!-- <div class="blogger-card mt-3" style="align-items: flex-start;">
    <div class="blogger-notice-title mb-2 text-left"><i class="fa fa-folder-open mr-1"></i>热门分类</div>
    <div class="d-flex flex-wrap">
        {volist name="hotCategories" id="cat"}
        <a href="/categoryList/{$cat.id}" class="tag-link mb-2 mr-3">{$cat.name}</a>
        {/volist}
    </div>
</div> -->
<!-- 热门标签卡片 -->
<!-- <div class="blogger-card mt-3" style="align-items: flex-start;">
    <div class="blogger-notice-title mb-2 text-left"><i class="fa fa-tags mr-1"></i>热门标签</div>
    <div class="d-flex flex-wrap">
        {volist name="hotTags" id="tag"}
        <a href="/tagList/{$tag.id}" class="tag-link mb-2 mr-3">{$tag.name}</a>
        {/volist}
    </div>
</div> -->
<!-- 广告位1 -->
<!-- <div class="blogger-card mt-3 p-0" style="background:none;box-shadow:none;">
    <a href="https://curl.qcloud.com/byhin5Ru" target="_blank" style="display:block;position:relative;">
        <img src="/uploads/ad1.png" alt="广告" style="width:100%;display:block;border-radius:8px;">
        <span style="
        position:absolute;
        top:10px;
        right:10px;
        background:rgba(0,0,0,0.65);
        color:#fff;
        font-size:0.95em;
        padding:2px 10px;
        border-radius:12px;
        letter-spacing:2px;
        z-index:2;
        font-weight:bold;
    ">广告</span>
    </a>
</div> -->
<div class="blogger-card">
    <div
        style="font-size: 1em;display: flex;align-items: center;width: 100%;border-bottom:1px solid rgba(255,255,255,0.08);height: 45px;padding: 0 15px;">
        <svg t="1752819535154" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="71163" width="22" height="22"><path d="M887.36 749.312c-18.176 18.048-50.752 38.528-104.128 40.32-24.704 0.896-53.504 0.96-66.688 0.96H429.696l205.696-199.808c9.472-9.216 30.72-29.568 49.152-46.08 40.32-36.288 76.544-43.584 102.208-43.328 40.192 0.384 76.8 16.768 102.848 43.264 55.936 57.152 54.592 148.48-2.24 204.672m68.736-269.696A236.16 236.16 0 0 0 787.2 408.32c-57.472 0-106.88 19.776-150.08 54.912-18.816 15.36-38.528 33.664-63.36 57.728-12.288 12.032-369.792 358.912-369.792 358.912 18.752 2.624 44.48 3.456 67.456 3.584 21.568 0.128 432.32 0.128 449.472 0.128 34.624 0 57.152-0.064 81.28-1.856 55.552-4.032 107.968-24.384 150.336-65.984a237.504 237.504 0 0 0 3.648-336.192z" fill="#00A3FF" p-id="71164"></path><path d="M379.328 457.28c-42.048-31.424-89.088-49.024-142.4-48.96a237.568 237.568 0 0 0-165.376 407.488c37.76 37.12 83.52 57.28 132.352 64.064l92.032-89.28c-14.848-0.064-36.224-0.256-55.168-0.896-53.376-1.856-85.888-22.336-104.128-40.32A145.024 145.024 0 0 1 134.4 544.64c26.112-26.496 62.72-42.88 102.848-43.264 25.216-0.128 59.392 7.168 98.048 39.232 18.432 15.296 59.392 51.2 77.312 67.456 0.896 0.832 2.048 0.896 3.072 0l63.36-61.824c1.152-1.024 1.088-2.496 0-3.456-30.528-27.52-73.728-66.112-99.712-85.504" fill="#00C8DC" p-id="71165"></path><path d="M811.968 354.688A318.336 318.336 0 0 0 512 142.656a318.72 318.72 0 0 0-314.368 268.992 236.608 236.608 0 0 1 92.992 3.008l0.96 0.192A225.28 225.28 0 0 1 512 235.776c89.984 0 168.896 54.272 204.48 131.136 0.576 1.216 1.536 1.6 2.496 1.344 26.752-7.36 58.88-11.648 89.792-9.344 3.072 0.192 4.224-1.472 3.2-4.224" fill="#006EFF" p-id="71166"></path></svg>
        <div style="margin-left: 4px;">广而告之</div>
    </div>
    <div style="width: 100%;box-sizing: border-box;overflow: hidden;">
        <a href="https://curl.qcloud.com/byhin5Ru" target="_blank" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;border-bottom-left-radius: 16px;border-bottom-right-radius: 16px;overflow: hidden;">
            <img src="/assets/img/480_300.png" alt="广告" style="width:100%;display:flex;padding: 0;margin: 0;border: 0;border-bottom-left-radius: 16px;border-bottom-right-radius: 16px;overflow: hidden;filter: brightness(0.5);">
        </a>
    </div>
</div>
<!-- 推荐文章卡片 -->
<!-- <div class="blogger-card mt-3" style="align-items: flex-start;">
    <div class="blogger-notice-title mb-2 text-left"><i class="fa fa-thumbs-up mr-1"></i>推荐文章</div>
    <ul class="list-group list-group-flush" style="width: 100%;">
        {volist name="recommendArticles" id="item" key="idx"}
        <li class="list-group-item px-0 py-2 border-0 d-flex align-items-center">
            <span class="rec-idx mr-2">{:sprintf('%02d', $idx)}</span>
            <a href="/detail/{$item.id}" class="tag-link text-truncate"
                style="width: 100%;">{$item.title}</a>
        </li>
        {/volist}
    </ul>
</div> -->
<div class="blogger-card">
    <div
        style="font-size: 1em;display: flex;align-items: center;width: 100%;border-bottom:1px solid rgba(255,255,255,0.08);height: 45px;padding: 0 15px;">
        <svg t="1752819090500" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="49929" width="22" height="22"><path d="M600.8 1020S844 986.4 928 770.4c0 0-66.4 53.6-93.6 70.4-27.2 16.8 14.4-47.2 49.6-80.8 34.4-33.6 69.6-112 16-202.4-53.6-90.4-32-127.2-20.8-171.2 0 0-71.2 21.6-88.8 67.2-17.6 45.6-30.4 84-32 31.2-0.8-52.8-48.8-187.2-79.2-248C648 176.8 633.6 89.6 698.4 0c0 0-240 44-316 219.2-75.2 174.4-78.4 241.6-96.8 170.4-17.6-71.2 10.4-85.6-56 9.6-66.4 95.2-101.6 221.6-60.8 353.6 0 0-38.4 4-72.8-36 0 0 8.8 249.6 349.6 302.4l155.2 0.8z" fill="#FF3600" p-id="49930"></path><path d="M260 523.2c28 9.6 52 26.4 69.6 48.8 18.4 24 24.8 50.4 28.8 78.4 12 86.4 76.8 126.4 76.8 126.4-45.6-41.6-60.8-80.8-64.8-136.8-5.6-79.2 12-136 50.4-203.2 46.4-81.6 132-147.2 218.4-184-68.8 48-89.6 125.6-63.2 213.6 36.8 84.8 84 133.6 128.8 218.4 24 52 52.8 108 25.6 188-13.6 40.8-56.8 93.6-122.4 120 16.8-15.2 33.6-40 36-80v-19.2c-7.2-96-101.6-149.6-101.6-149.6s4.8 42.4-10.4 85.6c-15.2 41.6-34.4 73.6-26.4 120 0 0-15.2-22.4-36.8-31.2-22.4-9.6-37.6-14.4-42.4-38.4 0 0-19.2 35.2-11.2 78.4 2.4 14.4 8.8 29.6 19.2 44.8l1.6 1.6C328 985.6 268 918.4 253.6 888c-27.2-55.2-24.8-117.6 9.6-186.4 19.2-36.8 61.6-140-3.2-178.4z" fill="#FFDA40" p-id="49931"></path><path d="M416.8 1014.4s-51.2-88.8-3.2-189.6c0 0-18.4 51.2 54.4 78.4 14.4 4 33.6 22.4 41.6 38.4 0 0-24.8-41.6 21.6-135.2 47.2-93.6 4-150.4 4-150.4s269.6 163.2 110.4 344c0 0-50.4 42.4-228.8 14.4z" fill="#FF3600" p-id="49932"></path></svg>
        <div style="margin-left: 4px;">推荐文章</div>
    </div>
    <div style="padding: 15px;width: 100%;box-sizing: border-box;">
        <ul class="rec-article-list">
            {volist name="recommendArticles" id="item" key="idx"}
            <li>
                <a href="/detail/{$item.id}" class="tag-link"
                    style="width: 100%;display: flex;align-items: center;text-decoration: none;">
                    <div class="ellipsis">{$item.title}</div>
                    <div style="display: flex;justify-content: flex-end;flex-grow: 1;">
                        <svg t="1752817040873" class="rec-link-svg" viewBox="0 0 1024 1024" fill="currentColor"
                            version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="40255" width="1em"
                            height="1em">
                            <path
                                d="M607.934444 417.856853c-6.179746-6.1777-12.766768-11.746532-19.554358-16.910135l-0.01228 0.011256c-6.986111-6.719028-16.47216-10.857279-26.930349-10.857279-21.464871 0-38.864146 17.400299-38.864146 38.864146 0 9.497305 3.411703 18.196431 9.071609 24.947182l-0.001023 0c0.001023 0.001023 0.00307 0.00307 0.005117 0.004093 2.718925 3.242857 5.953595 6.03853 9.585309 8.251941 3.664459 3.021823 7.261381 5.997598 10.624988 9.361205l3.203972 3.204995c40.279379 40.229237 28.254507 109.539812-12.024871 149.820214L371.157763 796.383956c-40.278355 40.229237-105.761766 40.229237-146.042167 0l-3.229554-3.231601c-40.281425-40.278355-40.281425-105.809861 0-145.991002l75.93546-75.909877c9.742898-7.733125 15.997346-19.668968 15.997346-33.072233 0-23.312962-18.898419-42.211381-42.211381-42.211381-8.797363 0-16.963347 2.693342-23.725354 7.297197-0.021489-0.045025-0.044002-0.088004-0.066515-0.134053l-0.809435 0.757247c-2.989077 2.148943-5.691629 4.669346-8.025791 7.510044l-78.913281 73.841775c-74.178443 74.229608-74.178443 195.632609 0 269.758863l3.203972 3.202948c74.178443 74.127278 195.529255 74.127278 269.707698 0l171.829484-171.880649c74.076112-74.17435 80.357166-191.184297 6.282077-265.311575L607.934444 417.856853z"
                                 p-id="40256"></path>
                            <path
                                d="M855.61957 165.804257l-3.203972-3.203972c-74.17742-74.178443-195.528232-74.178443-269.706675 0L410.87944 334.479911c-74.178443 74.178443-78.263481 181.296089-4.085038 255.522628l3.152806 3.104711c3.368724 3.367701 6.865361 6.54302 10.434653 9.588379 2.583848 2.885723 5.618974 5.355985 8.992815 7.309476 0.025583 0.020466 0.052189 0.041956 0.077771 0.062422l0.011256-0.010233c5.377474 3.092431 11.608386 4.870938 18.257829 4.870938 20.263509 0 36.68962-16.428158 36.68962-36.68962 0-5.719258-1.309832-11.132548-3.645017-15.95846l0 0c-4.850471-10.891048-13.930267-17.521049-20.210297-23.802102l-3.15383-3.102664c-40.278355-40.278355-24.982998-98.79612 15.295358-139.074476l171.930791-171.830507c40.179095-40.280402 105.685018-40.280402 145.965419 0l3.206018 3.152806c40.279379 40.281425 40.279379 105.838513 0 146.06775l-75.686796 75.737962c-10.296507 7.628748-16.97358 19.865443-16.97358 33.662681 0 23.12365 18.745946 41.87062 41.87062 41.87062 8.048303 0 15.563464-2.275833 21.944801-6.211469 0.048095 0.081864 0.093121 0.157589 0.141216 0.240477l1.173732-1.083681c3.616364-2.421142 6.828522-5.393847 9.529027-8.792247l79.766718-73.603345C929.798013 361.334535 929.798013 239.981676 855.61957 165.804257z"
                                 p-id="40257"></path>
                        </svg>
                    </div>
                </a>
            </li>
            {/volist}
        </ul>
    </div>
</div>
<style>
    .ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: block;
        /* 或 inline-block/flex，保证宽度生效 */
        max-width: 100%;
        margin-right: 15%;
        position: relative;
    }

    .rec-article-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .rec-article-list li {
        margin-bottom: 6px;
    }

    .rec-article-link {
        display: flex;
        align-items: center;
        color: #bdbdbd;
        font-size: 1.05em;
        text-decoration: none;
        border-radius: 6px;
        padding: 2px 0 2px 0;
        transition: color 0.18s, background 0.18s;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        gap: 8px;
    }

    .rec-article-link .rec-link-icon {
        height: 1.1em;
        color: #6c757d;
        transition: color 0.18s;
        flex-shrink: 0;
    }

    .rec-article-link:hover,
    .rec-article-link:focus {
        color: #7ecfff;
        background: rgba(80, 120, 200, 0.10);
        text-decoration: underline;
    }

    .rec-article-link:hover .rec-link-icon,
    .rec-article-link:focus .rec-link-icon {
        color: #7ecfff;
    }

    .tag-link {
        color: #6c757d;
        font-size: 0.9em;
        box-sizing: border-box;
        background: none;
        border-radius: 0;
        border: none;

        transition: color .2s, border-bottom .2s, font-weight .2s;
        border-bottom: 1px solid transparent;
        text-decoration: none;
    }

    .tag-link:hover {
        color: #fff;
    }

    .tag-link .ellipsis::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        height: 1px;
        width: 0;
        background: #ffff;
        border-radius: 2px;
        transition: width 0.32s cubic-bezier(.4, 2, .6, 1);
        pointer-events: none;
    }

    .tag-link:hover .ellipsis::after {
        width: 100%;
    }

    .rec-link-svg {
        transition: transform 0.32s cubic-bezier(.4, 2, .6, 1);
        color: #6c757d; /* 默认灰色 */
    }

    .tag-link:hover .rec-link-svg {
        transform: rotate(45deg);
        color: #fff;
    }

    .rec-idx {
        display: inline-block;
        width: 2em;
        text-align: center;
        color: #2563eb;
        font-weight: bold;
        font-size: 1.1em;
    }

    .rec-title {
        display: inline-block;
        vertical-align: middle;
        /* max-width: 140px; */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .list-group-item {
        background: transparent;
        border: none;
        padding-left: 0;
    }

    /* sticky 只在大屏生效 */
    @media (min-width: 992px) {
        aside.col-lg-4 {
            position: sticky;
            top: 100px;
            align-self: flex-start;
        }
    }
</style>
<style>
    .sidebar-fixed>.blogger-card, .order-lg-1>.blogger-card,.mobile-drawer-content>.blogger-card,.about-main-container>.blogger-card,
    .sidebar-fixed>.blogger-card.blogger-notice,
    .sidebar-fixed>.blogger-card.mt-3 {
        background: rgba(24, 26, 38, 0.96);
        border-radius: 16px;
        box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18), 0 0 12px 1px #3a3a5a33;
        border: 1.5px solid rgba(255, 255, 255, 0.08);
        color: #e0e0f0;
        margin-bottom: 15px;
        transition: background 0.18s, box-shadow 0.18s;
    }

    .sidebar-fixed .blogger-card .card-header,
    .sidebar-fixed .blogger-card .card-body,
    .sidebar-fixed .blogger-card .blogger-notice-title {
        background: transparent !important;
        color: #e0e0f0 !important;
        border: none !important;
    }

    .sidebar-fixed .blogger-card .blogger-nickname,
    .sidebar-fixed .blogger-card .blogger-fulljob,
    .sidebar-fixed .blogger-card .blogger-sidejob,
    .sidebar-fixed .blogger-card .blogger-signature {
        color: #e0e0f0 !important;
    }

    .sidebar-fixed .blogger-card .blogger-underline {
        background: #3a3a5a44;
    }
    .star-footer {
            background: rgba(24, 26, 38, 0.96);
            box-shadow: 0 -8px 32px 0 rgba(0, 0, 0, 0.25), 0 0 24px 2px #3a3a5a44;
            border-top: 1.5px solid rgba(255, 255, 255, 0.08);
            color: #e0e0f0;
            font-size: 16px;
            padding: 32px 0 18px 0;
            /* margin-top: 64px; */
            user-select: none;
            transition: opacity 0.18s, background 0.18s, box-shadow 0.18s;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .star-footer .container {
            max-width: 90vw;
            min-width: 320px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: width 0.2s;
        }

        .star-footer a {
            /* color: #7ecfff; */
            transition: color 0.18s;
        }

        .star-footer a:hover {
            color: #fff;
            text-decoration: underline;
        }
</style>
