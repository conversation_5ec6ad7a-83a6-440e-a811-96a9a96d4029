<!-- 顶部导航 fixed -->
<nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom fixed-top shadow-sm navbar-transparent" style="padding-left: 28px; padding-right: 28px;">
    <div class="navbar-container px-0">
        <div class="navbar-left-group d-flex align-items-center">
            <i class="fas fa-grip-vertical" style="font-size:1.4em;margin-right:8px;"></i>
            <a class="navbar-brand victorli-btn d-flex align-items-center home-hover-switch" href="/" style="letter-spacing:1px; position:relative;">
                <span class="home-text"><PERSON></span>
                <span class="home-icon"><i class="fas fa-home"></i></span>
            </a>
        </div>
        <!-- 移动端右侧按钮组，紧挨且靠右 -->
        <div class="d-flex align-items-center ml-auto d-lg-none">
            <a class="nav-link d-flex align-items-center justify-content-center search-btn-mobile" href="javascript:;" data-toggle="modal" data-target="#searchModal" style="margin-right:14px;">
                <i class="fa fa-search mr-1"></i>搜索
            </a>
            <button class="navbar-toggler custom-navbar-toggler collapsed" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-bar"></span>
            </button>
        </div>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ml-auto">
                <!-- <li class="nav-item"><a class="nav-link" href="/">首页</a></li> -->
                <li class="nav-item"><a class="nav-link" href="/archive">归档</a></li>
                <li class="nav-item"><a class="nav-link" href="/category">分类</a></li>
                <li class="nav-item"><a class="nav-link" href="/tag">标签</a></li>
                <li class="nav-item"><a class="nav-link" href="/message">留言</a></li>
                <li class="nav-item"><a class="nav-link" href="/links">友链</a></li>
                <li class="nav-item"><a class="nav-link" href="/about">关于</a></li>
                <!-- 电脑端搜索按钮，关于后面 -->
                <li class="nav-item d-none d-lg-block">
                    <a class="nav-link search-btn-desktop" href="javascript:;" data-toggle="modal" data-target="#searchModal">
                        <i class="fa fa-search mr-1"></i>搜索
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>
<style>
.navbar {
    background: rgba(24, 26, 38, 0.96) !important;
    /* border-radius: 14px; */
    box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25), 0 0 24px 2px #3a3a5a44;
    border: 1.5px solid rgba(255,255,255,0.08);
    color: #e0e0f0;
    font-size: 16px;
    padding: 8px 0;
    user-select: none;
    transition: opacity 0.18s, background 0.18s, box-shadow 0.18s;
}
.navbar .navbar-nav .nav-link {
    color: #e0e0f0 !important;
    font-size: 16px;
    transition: color 0.18s;
}
.navbar .navbar-nav .nav-link:hover, .navbar .navbar-nav .nav-link:focus {
    color: #fff !important;
}
.victorli-btn {
    background: none;
    color: #e0e0f0;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    box-shadow: none;
    cursor: pointer;
    text-decoration: none;
    border: none;
    position: relative;
    padding: 0 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    font-size: 1.18rem;
    font-weight: 700;
    letter-spacing: 1px;
}
.victorli-btn:hover, .victorli-btn:focus {
    background: rgba(80, 120, 200, 0.13);
  color: #fff;
  border-radius: 12px;
    text-decoration: none;
}
.custom-navbar-toggler {
    width: 40px;
    height: 40px;
    border: none;
    background: none;
    position: relative;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}
.navbar-toggler-bar,
.navbar-toggler-bar::before,
.navbar-toggler-bar::after {
    display: block;
    position: absolute;
    width: 26px;
    height: 3px;
    background: #e0e0f0;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(.4,2,.6,1);
    content: '';
}
.navbar-toggler-bar {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.navbar-toggler-bar::before {
    content: '';
    top: -9px;
    left: 0;
}
.navbar-toggler-bar::after {
    content: '';
    top: 9px;
    left: 0;
}
.custom-navbar-toggler.collapsed .navbar-toggler-bar {
    background: #e0e0f0;
}
.custom-navbar-toggler:not(.collapsed) .navbar-toggler-bar {
    background: transparent;
}
.custom-navbar-toggler:not(.collapsed) .navbar-toggler-bar::before {
    transform: translateY(9px) rotate(45deg);
}
.custom-navbar-toggler:not(.collapsed) .navbar-toggler-bar::after {
    transform: translateY(-9px) rotate(-45deg);
}
.custom-navbar-toggler.collapsed .navbar-toggler-bar::before,
.custom-navbar-toggler.collapsed .navbar-toggler-bar::after {
    transform: none;
}
.search-btn-mobile {
    border: none !important;
    background: none !important;
    outline: none !important;
    box-shadow: none !important;
    color: #e0f0 !important;
    transition: color 0.18s;
}
.search-btn-mobile:hover, .search-btn-mobile:focus {
    color: #fff !important;
    text-decoration: none;
    background: none !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}
.search-btn-desktop {
    border: none !important;
    background: none !important;
    outline: none !important;
    box-shadow: none !important;
    color: #e0f0 !important;
    transition: color 0.18s;
}
.search-btn-desktop:hover, .search-btn-desktop:focus {
    color: #fff !important;
    text-decoration: none;
    background: none !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}
@media (max-width: 600px) {
    nav.navbar {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
}
.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    width: 100%;
}
@media (max-width: 991px) {
    .navbar-container {
        max-width: none;
        margin: 0;
    }
}
.home-hover-switch {
    transition: background 0.18s, color 0.18s;
    border-radius: 12px;
    padding: 0 18px;
    box-sizing: border-box;
    position: relative;
}
.home-hover-switch:hover {
    background: rgba(80, 120, 200, 0.13);
    color: #fff;
}
.home-hover-switch .home-text {
    display: inline-block;
    transition: opacity 0.18s;
}
.home-hover-switch .home-icon {
    display: inline-block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.18s;
    color: #fff;
    font-size: 1.25em;
}
.home-hover-switch:hover .home-text {
    opacity: 0;
}
.home-hover-switch:hover .home-icon {
    opacity: 1;
}
</style>
<script src="/assets/js/jquery@3.5.1/jquery.min.js"></script>
<script>
// 切换菜单按钮图标（三横线/关闭X）
$(function(){
  $('#navbarNav').on('show.bs.collapse', function(){
    $('.icon-hamburger').hide();
    $('.icon-close').show();
  });
  $('#navbarNav').on('hide.bs.collapse', function(){
    $('.icon-hamburger').show();
    $('.icon-close').hide();
  });
});
</script>