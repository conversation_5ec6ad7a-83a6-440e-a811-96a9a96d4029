{/* 评论项组件 */}
<div class="comment-item">
    {include file="common/comment_single" item="$item" /}

    <span id="children-{$item.id}"></span>
   
    <!-- 加载更多子评论按钮 -->
    {if $item.children_count > 0}
    <div class="load-more-children" data-parent-id="{if isset($item.id)}{$item.id}{else}0{/if}" data-page="0"
        data-total="{$item.children_count}">
        <button type="button" class="btn-load-more-children">
            <i class="fa fa-spinner fa-spin me-2" style="display:none"></i>
            查看剩余 {$item.children_count} 条回复
        </button>
    </div>
    {/if}
</div>

<style>
    /* 评论中博主头像徽章样式 - 重命名避免冲突 */
    .comment-admin-avatar-badge {
        position: relative;
        width: 60px;
        height: 60px;
        margin-right: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .comment-admin-avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .comment-admin-avatar-badge-img {
        /* position: absolute; */
        width: 75px;
        height: 75px;
        margin-left: 10px;
        flex-shrink: 0;
        margin-right: 15px;
        border: 2px solid rgba(255, 255, 255, 0);
        z-index: 999;
    }

    /* 子评论中的博主头像 */
    .child-comment-admin-avatar {
        width: 40px;
        height: 40px;
    }



    /* 普通用户头像样式保持不变 */
    .comment-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: 15px;
        flex-shrink: 0;
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    /* 响应式调整 */
    @media (max-width: 700px) {
        .comment-admin-avatar-badge {
            width: 50px;
            height: 50px;
        }



        .child-comment-admin-avatar {
            width: 35px;
            height: 35px;
        }


    }
</style>