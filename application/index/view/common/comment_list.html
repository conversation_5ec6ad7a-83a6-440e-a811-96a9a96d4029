{/* 留言列表组件 */}
<div class="card" style="border-radius: 16px;overflow: hidden;">
    <div class="card-header d-flex justify-content-between align-items-center" style="padding: 0 15px;box-sizing: border-box;height: 45px;display: flex;align-items: center;border-radius: 0 !important;">
        <div>
            {if isset($list_title)}{$list_title}{else}评论列表{/if}
        </div>
        <div class="sort-btn-group" role="group">
            <button data-sort="desc" class="sort-btn {if $sort=='desc'}sort-btn-active{/if}">
                <i class="fa fa-sort-amount-down"></i> 最新
            </button>
            <button data-sort="asc" class="sort-btn {if $sort=='asc'}sort-btn-active{/if}">
                <i class="fa fa-sort-amount-up"></i> 最早
            </button>
            <button data-sort="hot" class="sort-btn {if $sort=='hot'}sort-btn-active{/if}">
                <i class="fa fa-fire"></i> 最热
            </button>
        </div>
    </div>
    <div class="card-body" style="padding: 15px;box-sizing: border-box;">
        <div class="comment-list">
            {if isset($commentList) && $commentList}
            {volist name="commentList" id="item"}
            {assign name="form_id" value="main-comment-form-form"}
            {include file="common/comment_item"/}
            {/volist}
            {else/}
            <div class="text-center text-muted py-5 empty-comment">
                <i class="fa fa-comments fa-3x mb-3"></i>
                <p>暂无评论，快来抢沙发吧！</p>
            </div>
            {/if}
        </div>
        <div class="text-center">
            <!-- <button id="loadMoreBtn" class="btn btn-outline-primary" data-page="{if isset($page)}{$page}{else}1{/if}">
                <i class="fa fa-spinner fa-spin me-2" style="display:none"></i>加载更多
            </button> -->
  {include file="common/paginator" total=$total limit=$limit page=$page urlss=$urlss query=$query  ajax="1"/}
        </div>
    </div>
</div>

<style>
/* 评论列表组件样式 */
.card {
    background: rgba(24,26,38,0.96);
    border-radius: 14px;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 0 12px 1px #3a3a5a33;
    border: 1.5px solid rgba(255,255,255,0.08);
    color: #e0e0f0;
    margin-bottom: 18px;
}

.card-header {
    background: rgba(36,38,48,0.98);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 12px 16px;
    border-radius: 14px 14px 0 0;
}

.card-body {
    padding: 16px;
    color: #e0e0f0;
}

/* 排序按钮组样式 */
.sort-btn-group {
    display: flex;
    gap: 2px;
    background: rgba(255,255,255,0.05);
    border-radius: 8px;
    padding: 2px;
    border: 1px solid rgba(255,255,255,0.1);
}

.sort-btn {
    background: transparent;
    border: none;
    color: #6c757d;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.sort-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(126,207,255,0.1), transparent);
    transition: left 0.3s ease;
}

.sort-btn:hover::before {
    left: 100%;
}

.sort-btn:hover {
    color: #7ecfff;
    background: rgba(126,207,255,0.1);
    transform: translateY(-1px);
}

.sort-btn-active {
    background: linear-gradient(135deg, #7ecfff 0%, #5a9eff 100%);
    color: #1a1a1a;
    box-shadow: 0 2px 6px rgba(126, 207, 255, 0.3);
    font-weight: 600;
}

.sort-btn-active:hover {
    color: #1a1a1a;
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(126, 207, 255, 0.4);
}

.sort-btn i {
    font-size: 11px;
    opacity: 0.8;
}

.sort-btn:hover i {
    opacity: 1;
}

.sort-btn-active i {
    opacity: 1;
}

.comment-list {
    margin-bottom: 16px;
}

.comment-item {
    background: rgba(36,38,48,0.98);
    border-radius: 12px;
    padding: 15px;
    box-sizing: border-box;
    margin-bottom: 15px;
    border: 1px solid rgba(255,255,255,0.06);
    transition: all 0.2s ease;
}

.comment-item:hover {
    border-color: rgba(126,207,255,0.2);
    box-shadow: 0 2px 8px rgba(126,207,255,0.1);
}

.comment-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0px;
}

.comment-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
    border: 2px solid rgba(255,255,255,0.1);
}

.comment-user-info {
    flex: 1;
    min-width: 0;
}

.comment-meta {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
}

.comment-nickname {
    font-weight: 600;
    color: #e0e0f0;
    font-size: 1rem;
}

.comment-nickname a {
    color: #7ecfff;
    text-decoration: none;
}

.comment-nickname a:hover {
    color: #5a9eff;
    text-decoration: underline;
}

.comment-time {
    color: #6c757d;
    /* font-size: 12px; */
    margin-left: 8px;
}

.comment-actions {
    display: flex;
    gap: 8px;
}

.btn-like, .btn-reply {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #e0e0f0;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-like:hover, .btn-reply:hover {
    background: rgba(126,207,255,0.2);
    border-color: #7ecfff;
    color: #7ecfff;
}

.btn-like.liked {
    background: rgba(255,107,107,0.2);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.comment-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 4px;
}

.comment-detail {
    color: #6c757d;
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 2px;
}

.comment-content {
    color: #e0e0f0;
    font-size: 1rem;
    margin-bottom: 15px;
    margin-left: 75px;
}

.comment-content p {
    margin: 0 0 8px 0;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.children-comments {
    margin-left: 75px;
    /* margin-top: 12px; */
    /* padding-left: 16px; */
    /* border-left: 2px solid rgba(255,255,255,0.1); */
}

.child-comment {
    /* background: rgba(24,26,38,0.8); */
    border-radius: 8px;
    /* padding: 12px; */
    /* margin-bottom: 8px; */
    /* border: 1px solid rgba(255,255,255,0.05); */
}

.child-comment .comment-avatar {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.child-comment .comment-nickname {
    font-size: 13px;
}

.child-comment .comment-time {
    font-size: 11px;
}

.child-comment .comment-content {
    font-size: 13px;
    margin-bottom: 8px;
}

.reply-info {
    background: rgba(126,207,255,0.1);
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 8px;
    border-left: 3px solid #7ecfff;
}

.reply-info small {
    color: #6c757d;
    font-size: 12px;
}

.reply-form-container {
    margin-top: 12px;
}

.reply-card {
    background: rgba(24,26,38,0.96);
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.08);
    margin-bottom: 12px;
}

.reply-card .card-header {
    background: rgba(36,38,48,0.98);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 12px 16px;
    border-radius: 12px 12px 0 0;
}

.reply-card .card-body {
    padding: 16px;
}

.btn-close-reply {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #e0e0f0;
    border-radius: 6px;
    padding: 4px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-close-reply:hover {
    background: rgba(255,107,107,0.2);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.load-more-children {
    text-align: center;
    margin-top: 0px;
}

.btn-load-more-children {
    background: rgba(126,207,255,0.1);
    border: 1px solid rgba(126,207,255,0.3);
    color: #7ecfff;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 auto;
}

.btn-load-more-children:hover {
    background: rgba(126,207,255,0.2);
    border-color: #7ecfff;
}

.empty-comment {
    color: #6c757d;
    padding: 40px 20px;
}

.empty-comment i {
    color: #6c757d;
    margin-bottom: 16px;
}

.load-more-container {
    text-align: center;
    margin-top: 20px;
}

#loadMoreBtn {
    background: rgba(126,207,255,0.1);
    border: 1px solid rgba(126,207,255,0.3);
    color: #7ecfff;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
}

#loadMoreBtn:hover {
    background: rgba(126,207,255,0.2);
    border-color: #7ecfff;
}

#loadMoreBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

@media (max-width: 700px) {
    .card {
        border-radius: 8px;
    }
    .card-header {
        border-radius: 8px 8px 0 0;
        padding: 10px 12px;
    }
    .card-body {
        padding: 12px;
    }
    .comment-nickname {
        font-size: 13px;
    }
    .comment-time {
        font-size: 11px;
    }
    .comment-content {
        font-size: 13px;
    }
    .children-comments {
        /* margin-left: 16px; */
        /* padding-left: 12px; */
    }
    .child-comment {
        padding: 8px;
        border-radius: 6px;
    }
    .child-comment .comment-avatar {
        width: 28px;
        height: 28px;
        margin-right: 6px;
    }
    .child-comment .comment-nickname {
        font-size: 12px;
    }
    .child-comment .comment-content {
        font-size: 12px;
    }
    .btn-like, .btn-reply {
        padding: 3px 6px;
        font-size: 11px;
    }
    .comment-actions {
        gap: 6px;
    }
    .sort-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
    .sort-btn i {
        font-size: 10px;
    }
}
</style> 