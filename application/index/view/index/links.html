{extend name="layout/blog" /}

{block name="content"}
<div class="about-main-container">
  <!-- 关于友链 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      关于友链
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <p>欢迎交换友链！本站致力于分享技术、记录成长，欢迎内容优质、积极向上的网站互加友链。</p>
      <ul>
        <li>请确保您的网站内容健康、无违规</li>
        <li>优先收录原创技术博客、产品站、设计类网站</li>
        <li>如需交换，请在下方填写申请表，或通过邮箱联系我</li>
      </ul>
    </div>
  </div>

  <!-- 推荐友链 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      推荐友链
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="friend-link-list">
        {volist name="linkList" id="link"}
        <a href="{$link.url}" target="_blank" class="friend-link-card" style="text-decoration: none;">
          <img src="{$link.logo|default='/assets/img/logo_default.png'}" class="friend-link-logo" alt="{$link.name}">
          <div class="friend-link-info">
            <div class="friend-link-title">{$link.name}</div>
            <div class="friend-link-desc">{$link.description|default=''} </div>
          </div>
        </a>
        {/volist}
      </div>
    </div>
  </div>

  <!-- 收藏网站 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      收藏网站
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="friend-link-list">
        {volist name="favoriteList" id="fav"}
        <a href="{$fav.url}" target="_blank" class="friend-link-card" style="text-decoration: none;">
          <img src="{$fav.logo|default='/assets/img/logo_default.png'}" class="friend-link-logo" alt="{$fav.name}">
          <div class="friend-link-info">
            <div class="friend-link-title">{$fav.name}</div>
            <div class="friend-link-desc">{$fav.description|default=''} </div>
          </div>
        </a>
        {/volist}
      </div>
    </div>
  </div>

  <!-- 本站信息 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      本站信息
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="code-block">
        <div class="code-header">
          <div class="window-controls">
            <span class="control red"></span>
            <span class="control yellow"></span>
            <span class="control green"></span>
          </div>
          <span class="code-type">YAML</span>
          <div class="code-actions">
            <i class="fa fa-copy" onclick="copySiteInfo()"></i>
          </div>
        </div>
        <div class="code-content">
          <div class="code-lines">
            <div class="line-numbers">
              <span>1</span>
              <span>2</span>
              <span>3</span>
              <span>4</span>
              <span>5</span>
            </div>
            <div class="code-text">
              <pre><code>name: 德胜独立开发
link: https://deshengdulikaifa.com
avatar: https://deshengdulikaifa.com/uploads/20250627/a68d5863c4efdfd519f25c58a4a4d874.png
descr: 分享技术、记录成长
email: <EMAIL></code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 申请友链 -->
  <div class="blogger-card about-card" style="margin-bottom: 0;">
    <div class="about-card-title">
      申请友链
      <button type="submit" class="submit-btn" form="link-apply-form">提交申请</button>
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <form class="apply-form" id="link-apply-form" method="post" enctype="multipart/form-data" action="/api/links/apply">
        <div class="form-row">
          <div class="form-label">网站名称</div>
          <input type="text" class="form-input" id="name" name="name" required placeholder="请输入网站名称">
        </div>
        <div class="form-row">
          <div class="form-label">网站链接</div>
          <input type="url" class="form-input" id="url" name="url" required placeholder="请输入网站链接">
        </div>
        <div class="form-row">
          <div class="form-label">网站头像</div>
          <input type="url" class="form-input" id="logo" name="logo" placeholder="请输入网站头像">
        </div>
        <div class="form-row">
          <div class="form-label">网站描述</div>
          <input type="text" class="form-input" id="description" name="description" maxlength="40" required placeholder="请输入网站描述">
        </div>
        <div class="form-row">
          <div class="form-label">电子邮箱</div>
          <input type="email" class="form-input" id="email" name="email" required placeholder="请输入您的邮箱">
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  .about-main-container {
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
  }

  p,
  ul {
    margin: 0;
  }

  .about-card {
    background: rgba(24, 26, 38, 0.96);
    border-radius: 18px;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18), 0 0 12px 1px #3a3a5a33;
    border: 1.5px solid rgba(255, 255, 255, 0.08);
    color: #e0e0f0;
    /* padding: 22px 18px 18px 18px; */
    /* margin-bottom: 24px; */
    overflow: hidden;
    position: relative;
  }

  .about-card-title {
    font-size: 1em;
    font-weight: bold;
    color: #e0e0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-sizing: border-box;
    height: 45px;
  }

  .about-card-divider {
    height: 1.5px;
    background: #4445;
    /* margin-bottom: 12px; */
    border-radius: 1px;
  }

  .about-card-content {
    color: #6c757d;
    font-size: 1em;
    word-break: break-all;
  padding: 15px;
  box-sizing: border-box;
  }

  .friend-link-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  .friend-link-card {
    flex: 1 1 calc(33.333% - 20px);
    box-sizing: border-box;
    cursor: pointer;
    transition: box-shadow 0.2s;
    background: rgba(36, 38, 48, 0.98);
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.10);
    border: 1px solid rgba(255, 255, 255, 0.06);
    color: #e0e0f0;
    padding: 14px 16px 12px 16px;
    display: flex;
    align-items: center;
    position: relative;
    text-decoration: none;
  }

  .friend-link-card:hover {
    box-shadow: 0 4px 16px 0 rgba(80, 120, 200, 0.13);
    border: 1.5px solid #7ecfff44;
  }

  .favorite-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #ffb400;
    font-size: 20px;
    cursor: pointer;
    z-index: 2;
  }

  @media (max-width: 768px) {
    .friend-link-card {
      flex-basis: calc(50% - 20px);
    }
  }

  @media (max-width: 480px) {
    .friend-link-card {
      flex-basis: calc(50% - 20px);
    }
  }

  @media (max-width: 360px) {
    .friend-link-card {
      flex-basis: 100%;
    }
  }

  .friend-link-logo {
    width: 44px;
    height: 44px;
    border-radius: 10px;
    object-fit: cover;
    background: #fff;
    box-shadow: 0 2px 8px #0002;
    flex-shrink: 0;
    margin-right: 12px;
  }

  .friend-link-info {
    flex: 1;
    min-width: 0;
  }

  .friend-link-title {
    font-size: 1em;
    font-weight: bold;
    color: #e0e0f0;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .friend-link-desc {
    color: #6c757d;
    font-size: 0.98em;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-word;
    text-overflow: ellipsis;
  }

  .friend-link-btn {
    color: #7ecfff;
    font-size: 1.1em;
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 8px;
    background: rgba(80, 120, 200, 0.08);
    transition: background 0.18s, color 0.18s;
    margin-left: 8px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .friend-link-btn:hover {
    background: #7ecfff;
    color: #222;
  }

  .custom-input {
    background: #f5f6fa !important;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    color: #333;
  }

  .custom-input:focus {
    border-color: #b3b3b3;
    outline: none;
  }

  /* 申请友链表单样式 */
  .apply-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    display: flex;
    align-items: center;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .form-label {
    background: rgba(255, 255, 255, 0.1);
    color: #e0e0f0;
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
    min-width: 80px;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
  }

  .form-input {
    background: rgba(36, 38, 48, 0.98);
    border: none;
    padding: 12px 16px;
    color: #e0e0f0;
    font-size: 14px;
    transition: all 0.2s ease;
    box-sizing: border-box;
    flex: 1;
    min-width: 0;
  }

  .form-input::placeholder {
    color: #6c757d;
  }

  .form-input:focus {
    outline: none;
    background: rgba(36, 38, 48, 1);
  }

  .form-submit {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .submit-btn {
    background: linear-gradient(135deg, #7ecfff 0%, #5a9eff 100%);
    color: #1a1a1a;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(126, 207, 255, 0.3);
    height: 32px;
    line-height: 1;
  }

  .submit-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(126, 207, 255, 0.4);
  }

  .submit-btn:active {
    transform: translateY(0);
  }

  /* 响应式布局 */
  @media (min-width: 768px) {
    .apply-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    
    .form-submit {
      grid-column: 1 / -1;
      margin-top: 0;
    }
  }

  .code-block {
    background: #2d2d2d;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #444;
    /* margin: 10px 0; */
    width: 100%;
    box-sizing: border-box;
  }

  .code-header {
    background: #3c3c3c;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #444;
  }

  .window-controls {
    display: flex;
    gap: 6px;
  }

  .control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .control.red {
    background: #ff5f56;
  }

  .control.yellow {
    background: #ffbd2e;
  }

  .control.green {
    background: #27ca3f;
  }

  .code-type {
    color: #b4b4b4;
    font-size: 12px;
    font-weight: 500;
  }

  .code-actions {
    display: flex;
    gap: 8px;
    color: #b4b4b4;
  }

  .code-actions i {
    cursor: pointer;
    font-size: 14px;
  }

  .code-actions i:hover {
    color: #fff;
  }

  .code-content {
    padding: 0;
    background: #2d2d2d;
    overflow-x: auto;
    width: 100%;
    box-sizing: border-box;
  }

  .code-lines {
    display: flex;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    width: 100%;
    box-sizing: border-box;
  }

  .line-numbers {
    background: #1e1e1e;
    padding: 16px 8px 16px 16px;
    box-sizing: border-box;
    border-right: 1px solid #444;
    color: #666;
    text-align: right;
    user-select: none;
    min-width: 40px;
  }

  .line-numbers span {
    display: block;
    font-size: 12px;
  }

  .code-text {
    /* flex: 1; */
    width: 100%;
    padding: 16px 16px 16px 8px;
    box-sizing: border-box;
    min-width: 0;
    box-sizing: border-box;
  }

  .code-text pre {
    margin: 0;
    color: #e0e0e0;
    font-size: 12px;
  }
  .code-text code {
    color: #e0e0e0;
    
  }

  /* 自定义滚动条样式 */
  .code-content::-webkit-scrollbar,
  .code-text::-webkit-scrollbar {
    height: 8px;
  }

  .code-content::-webkit-scrollbar-track,
  .code-text::-webkit-scrollbar-track {
    background: #1e1e1e;
    border-radius: 4px;
  }

  .code-content::-webkit-scrollbar-thumb,
  .code-text::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
  }

  .code-content::-webkit-scrollbar-thumb:hover,
  .code-text::-webkit-scrollbar-thumb:hover {
    background: #777;
  }
</style>
<script>
  function copySiteInfo() {
    var logo = "https://deshengdulikaifa.com{$site.site_logo}";
    var title = "德胜独立开发";
    var desc = "{$site.description|default='分享技术、记录成长'}";
    var text = `name: ${title}
link: https://deshengdulikaifa.com
avatar: ${logo}
descr: ${desc}
email: <EMAIL>`;

    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(function () {
        showToast('复制成功！');
      }, function () {
        fallbackCopyTextToClipboard(text);
      });
    } else {
      fallbackCopyTextToClipboard(text);
    }
  }

  function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      showToast('复制成功！');
    } catch (err) {
      showToast('复制失败，请手动复制');
    }
    document.body.removeChild(textArea);
  }
  function collectSite(id) {
    showToast('收藏功能开发中');
    // TODO: 实现收藏逻辑，可用ajax提交到后端
  }
  document.addEventListener('DOMContentLoaded', function () {
    var form = document.getElementById('link-apply-form');
    if (form) {
      form.addEventListener('submit', function (e) {
        e.preventDefault();
        var formData = new FormData(form);
        var xhr = new XMLHttpRequest();
        xhr.open('POST', form.action, true);
        xhr.onload = function () {
          try {
            var res = JSON.parse(xhr.responseText);
            if (res.code == 1) {
              showToast('提交成功，等待审核');
              form.reset();
            } else {
              showToast(res.msg || '提交失败');
            }
          } catch (err) {
            showToast('提交失败');
          }
        };
        xhr.onerror = function() {
          showToast('网络错误，提交失败');
        };
        xhr.send(formData);
      });
    }
  });
</script>
{/block}