<div class="comment-item">
    <div class="comment-header">
        <img src="{$item.avatar_url|default='/assets/img/default-avatar.jpeg'}"
             class="comment-avatar" alt="{$item.nickname}"
             onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
        <div class="comment-user-info">
            <div class="comment-meta">
                <span>
                    <span class="comment-nickname">
                        {if !empty($item.website)}
                        <a href="{$item.website}" target="_blank" class="text-decoration-none">{$item.nickname}</a>
                        {else/}
                        {$item.nickname}
                        {/if}
                        {if isset($item.is_admin) && $item.is_admin}
                        <span class="badge bg-warning text-dark ms-1">博主</span>
                        {/if}
                    </span>
                    <span class="comment-time">{$item.createtime_formatted}</span>
                </span>
                <div class="comment-actions">
                    <button type="button" class="btn-like" data-id="{$item.id}">
                        <i class="fa fa-heart"></i> 点赞 <span>{$item.likes|default=0}</span>
                    </button>
                    <button type="button" class="btn-reply" data-id="{$item.id}">
                        <i class="fa fa-reply"></i> 回复
                    </button>
                </div>
            </div>
            <div class="comment-details">
                {if !empty($item.ip_location)}
                <span class="comment-detail"><i class="fa fa-map-marker-alt"></i> {$item.ip_location}</span>
                {/if}
                {if !empty($item.device)}
                <span class="comment-detail"><i class="fa fa-mobile-alt"></i> {$item.device}</span>
                {/if}
                {if !empty($item.browser)}
                <span class="comment-detail"><i class="fa fa-globe"></i> {$item.browser}</span>
                {/if}
            </div>
        </div>
    </div>
    <div class="comment-content">{$item.content_formatted}</div>
    <div class="reply-form-container" id="reply-form-{$item.id}" style="display:none;">
        <div class="card mb-4 reply-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fa fa-pen me-1"></i> 回复 {$item.nickname}</span>
                <button type="button" class="btn-close-reply btn btn-sm btn-light" style="font-size:1.2rem;">×</button>
            </div>
            <div class="card-body">
                <form class="comment-form" method="post" action="/api/comment/add">
                    <input type="hidden" name="type" value="{$type}">
                    {if isset($article_id) && $article_id}
                    <input type="hidden" name="article_id" value="{$article_id}">
                    {/if}
                    <input type="hidden" name="parent_id" value="{$item.id}">
                    <input type="hidden" name="reply_to_id" value="0">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">昵称 <span class="text-danger">*</span></label>
                            <input type="text" name="nickname" class="form-control" placeholder="请输入昵称" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                            <input type="email" name="email" class="form-control" placeholder="请输入邮箱" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">网址（可选）</label>
                            <input type="url" name="website" class="form-control" placeholder="可选，带 http(s)://">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">留言内容 <span class="text-danger">*</span></label>
                        <textarea name="content" class="form-control" rows="4" placeholder="欢迎留言，分享你的想法..." required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary px-4">发表留言</button>
                </form>
            </div>
        </div>
    </div>
    <!-- 子评论区域 -->
    <div class="children-comments" id="children-{$item.id}">
        {if isset($item.first_child) && $item.first_child}
        <div class="child-comment">
            <div class="comment-header">
                <img src="{$item.first_child.avatar_url|default='/assets/img/default-avatar.jpeg'}"
                    class="comment-avatar" alt="{$item.first_child.nickname}"
                    onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
                <div class="comment-user-info">
                    <div class="comment-meta">
                        <span>
                            <span class="comment-nickname">
                                {if !empty($item.first_child.website)}
                                <a href="{$item.first_child.website}" target="_blank" class="text-decoration-none">{$item.first_child.nickname}</a>
                                {else/}
                                {$item.first_child.nickname}
                                {/if}
                                {if isset($item.first_child.is_admin) && $item.first_child.is_admin}
                                <span class="badge bg-warning text-dark ms-1">博主</span>
                                {/if}
                            </span>
                            <span class="comment-time">{$item.first_child.createtime_formatted}</span>
                        </span>
                        <div class="comment-actions">
                            <button type="button" class="btn-like" data-id="{$item.first_child.id}">
                                <i class="fa fa-heart"></i> 点赞 <span>{$item.first_child.likes|default=0}</span>
                            </button>
                            <button type="button" class="btn-reply" data-id="{$item.first_child.id}">
                                <i class="fa fa-reply"></i> 回复
                            </button>
                        </div>
                    </div>
                    <div class="comment-details">
                        {if !empty($item.first_child.ip_location)}
                        <span class="comment-detail"><i class="fa fa-map-marker-alt"></i> {$item.first_child.ip_location}</span>
                        {/if}
                        {if !empty($item.first_child.device)}
                        <span class="comment-detail"><i class="fa fa-mobile-alt"></i> {$item.first_child.device}</span>
                        {/if}
                        {if !empty($item.first_child.browser)}
                        <span class="comment-detail"><i class="fa fa-globe"></i> {$item.first_child.browser}</span>
                        {/if}
                    </div>
                </div>
            </div>
            <div class="comment-content">
                {if isset($item.first_child.reply_to_nickname)}
                <div class="reply-info mb-2">
                    <small class="text-muted">
                        <i class="fa fa-reply"></i> 回复 @
                        {if !empty($item.first_child.reply_to_website)}
                        <a href="{$item.first_child.reply_to_website}" target="_blank" class="text-decoration-none">{$item.first_child.reply_to_nickname}</a>
                        {else/}
                        {$item.first_child.reply_to_nickname}
                        {/if}
                        ：{$item.first_child.reply_to_content}
                    </small>
                </div>
                {/if}
                {$item.first_child.content_formatted}
            </div>
            <div class="reply-form-container" id="reply-form-{$item.first_child.id}" style="display:none;">
                <div class="card mb-4 reply-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fa fa-pen me-1"></i> 回复 {$item.first_child.nickname}</span>
                        <button type="button" class="btn-close-reply btn btn-sm btn-light" style="font-size:1.2rem;">×</button>
                    </div>
                    <div class="card-body">
                        <form class="comment-form" method="post" action="/api/comment/add">
                            <input type="hidden" name="type" value="{$type}">
                            {if isset($article_id) && $article_id}
                            <input type="hidden" name="article_id" value="{$article_id}">
                            {/if}
                            <input type="hidden" name="parent_id" value="{$item.id}">
                            <input type="hidden" name="reply_to_id" value="{$item.first_child.id}">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">昵称 <span class="text-danger">*</span></label>
                                    <input type="text" name="nickname" class="form-control" placeholder="请输入昵称" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                                    <input type="email" name="email" class="form-control" placeholder="请输入邮箱" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">网址（可选）</label>
                                    <input type="url" name="website" class="form-control" placeholder="可选，带 http(s)://">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">留言内容 <span class="text-danger">*</span></label>
                                <textarea name="content" class="form-control" rows="4" placeholder="欢迎留言，分享你的想法..." required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary px-4">发表留言</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {/if}
        <!-- 加载更多子评论按钮 -->
        {if $item.children_count > 1}
        <div class="load-more-children" data-parent-id="{$item.id}" data-page="0" data-total="{$item.children_count-1}">
            <button type="button" class="btn-load-more-children">
                <i class="fa fa-spinner fa-spin me-2" style="display:none"></i>
                查看剩余 {$item.children_count-1} 条回复
            </button>
        </div>
        {/if}
    </div>
</div> 