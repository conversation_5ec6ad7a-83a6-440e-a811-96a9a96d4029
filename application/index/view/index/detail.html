{extend name="layout/blog" /}

{block name="content"}
<div style="width: 100%;padding: 0;">
    <div class="article-main-content mx-auto">
        <article class="card mb-4 article-card" style="margin-bottom: 15px !important;">
            <div class="card-body">
                <!-- 标题和基础信息 -->
                <h1 class="card-title article-title">{$article.title}</h1>
                <div class="article-meta text-muted border-bottom pb-3 mb-3" style="border-top:0;padding-top: 0;display: flex;justify-content: space-between;align-items: center;">
                    <span>
<span class="post-meta-views"><span> {$article.views} 阅读</span></span>
<span class="post-meta-sep">·</span>
<span class="post-meta-time">更新于{:date('Y-m-d', $article.createtime)} {$article.daypart|default=''}</span>

                    </span>
                    <span><i class="fa fa-folder-open me-1"></i>
                      <a href="/categoryList/{$article.category_id}" style="text-decoration:none;" class="article-category-link">{$article.category_name|default=''}</a>
                    </span>
                </div>
                <!-- 摘要 -->
                {if $article.summary}
                <div class="article-summary-block star-sky-summary">
  <div class="star-sky-summary-content">
                    {$article.summary}
  </div>
                </div>
                {/if}
                <!-- 正文内容 -->
                <div class="article-content">
                    {$article.content}
                </div>
                <!-- 标签 -->
                {if !empty($article)}
                <div class="article-tags pt-3 border-top">
                    {volist name="article.tags" id="tag"}
                    <span><a href="/tagList/{$tag.id}" style="text-decoration:none;margin: 0;" class="article-tag-link">{$tag.name}</a></span>
                    {/volist}
                </div>
                {/if}
                
            </div>
        </article>
        <!-- 上一篇/下一篇导航 -->
        {if !empty($prev) || !empty($next)}
        <div class="article-nav-row">
          <!-- 上一篇 -->
            {if !empty($prev)}
          <a href="/detail/{$prev.id}" class="article-nav-half article-nav-half-left" style="text-decoration:none;background-image: url('{$prev.image|default='/assets/img/cover_default.png'}');">
            <div class="nav-half-label">上一篇</div>
            <div class="nav-half-title">{$prev.title|default=''}</div>
            </a>
            {else/}
          <div class="article-nav-half article-nav-half-left nav-half-disabled" style="text-decoration:none;background-image: url('/assets/img/cover_default.png');">
            <div class="nav-half-label">上一篇</div>
            <div class="nav-half-title">没有啦~</div>
          </div>
            {/if}
          <!-- 下一篇 -->
            {if !empty($next)}
          <a href="/detail/{$next.id}" class="article-nav-half article-nav-half-right" style="text-decoration:none;background-image: url('{$next.image|default='/assets/img/cover_default.png'}');">
            <div class="nav-half-label">下一篇</div>
            <div class="nav-half-title">{$next.title|default=''}</div>
            </a>
            {else/}
          <div class="article-nav-half article-nav-half-right nav-half-disabled" style="text-decoration:none;background-image: url('/assets/img/cover_default.png');">
            <div class="nav-half-label">下一篇</div>
            <div class="nav-half-title">没有啦~</div>
          </div>
            {/if}
        </div>
        {/if}
        
        <!-- 打赏模块 -->
        <div class="donate-simple-card">
          <div class="donate-simple-tip">喜欢这篇文章嘛，觉得文章不错的话，奖励奖励我！</div>
          <div class="donate-simple-btns">
            <div class="donate-btn-wrap">
              <button class="donate-btn donate-btn-alipay">
                <span class="icon-wrap">
                  <svg t="1753000835344" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="33864" width="22" height="22"><path d="M1024.0512 701.0304V196.864A196.9664 196.9664 0 0 0 827.136 0H196.864A196.9664 196.9664 0 0 0 0 196.864v630.272A196.9152 196.9152 0 0 0 196.864 1024h630.272a197.12 197.12 0 0 0 193.8432-162.0992c-52.224-22.6304-278.528-120.32-396.4416-176.64-89.7024 108.6976-183.7056 173.9264-325.3248 173.9264s-236.1856-87.2448-224.8192-194.048c7.4752-70.0416 55.552-184.576 264.2944-164.9664 110.08 10.3424 160.4096 30.8736 250.1632 60.5184 23.1936-42.5984 42.496-89.4464 57.1392-139.264H248.064v-39.424h196.9152V311.1424H204.8V267.776h240.128V165.632s2.1504-15.9744 19.8144-15.9744h98.4576V267.776h256v43.4176h-256V381.952h208.8448a805.9904 805.9904 0 0 1-84.8384 212.6848c60.672 22.016 336.7936 106.3936 336.7936 106.3936zM283.5456 791.6032c-149.6576 0-173.312-94.464-165.376-133.9392 7.8336-39.3216 51.2-90.624 134.4-90.624 95.5904 0 181.248 24.4736 284.0576 74.5472-72.192 94.0032-160.9216 150.016-253.0816 150.016z" fill="#009FE8" p-id="33865"></path></svg>
                </span>
                支付宝
              </button>
              <div class="donate-qr-pop" id="alipayQrPop">
                <img src="/assets/img/zfb.jpg" alt="支付宝二维码">
                <!-- <div class="donate-qr-arrow"></div> -->
                <div class="donate-qr-label">扫码打赏</div>
              </div>
            </div>
            <div class="donate-btn-wrap">
              <button class="donate-btn donate-btn-wechat">
                <span class="icon-wrap">
                  <svg t="1753000861136" class="icon" viewBox="0 0 1144 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="34879" width="22" height="22"><path d="M436.314353 632.771765c-68.517647 36.321882-78.667294-20.389647-78.667294-20.389647l-85.835294-190.524236c-33.039059-90.533647 28.581647-40.839529 28.581647-40.839529s52.856471 38.038588 93.003294 61.229176c40.086588 23.190588 85.835294 6.806588 85.835294 6.806589l561.212235-246.362353C936.899765 80.112941 765.891765 0 572.235294 0 256.180706 0 0 213.232941 0 476.310588c0 151.311059 84.811294 285.967059 216.937412 373.248l-23.792941 130.288941s-11.625412 38.038588 28.611764 20.389647c27.437176-12.047059 97.370353-55.115294 138.992941-81.347764 65.445647 21.684706 136.734118 33.731765 211.486118 33.731764 316.024471 0 572.235294-213.232941 572.235294-476.310588 0-76.197647-21.594353-148.178824-59.843764-212.028235-178.808471 102.309647-594.733176 340.118588-648.312471 368.489412z" fill="#43C93E" p-id="34880"></path></svg>
                </span>
                微信
              </button>
              <div class="donate-qr-pop" id="wechatQrPop">
                <img src="/assets/img/wx.jpg" alt="微信二维码">
                <!-- <div class="donate-qr-arrow"></div> -->
                <div class="donate-qr-label">扫码打赏</div>
              </div>
            </div>
          </div>
        </div>

        <style>
.donate-simple-card {
  background: rgba(36,38,48,0.92);
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(126,207,255,0.08);
  padding: 15px;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  margin: 0 auto 18px auto;
  /* max-width: 400px; */
}
.donate-simple-tip {
  color: #888;
  font-size: 1em;
  margin-bottom: 15px;
  font-weight: 500;
  letter-spacing: 1px;
}
.donate-simple-btns {
  display: flex;
  justify-content: center;
  gap: 18px;
}
.donate-btn-wrap {
                position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.donate-btn {
  border: none;
  outline: none;
  border-radius: 8px;
  font-size: 1.12em;
  font-weight: 700;
  padding: 10px 28px;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.15s, box-shadow 0.15s;
  background: #1890ff;
}
.donate-btn-wechat { background: #1aad19; }
.donate-btn-alipay { background: #1890ff; }
.donate-btn-alipay:hover { background: #1177cc; }
.donate-btn-wechat:hover { background: #128c13; }
.icon-wrap { display: flex; align-items: center; }
.donate-btn svg { width: 22px; height: 22px; display: block; }

.donate-qr-pop {
  display: none;
  position: absolute;
  left: 50%;
  bottom: 110%;
  transform: translateX(-50%);
  background: #23243a;
  border-radius: 10px;
  box-shadow: 0 4px 24px 0 rgba(126,207,255,0.18);
  padding: 14px 14px 8px 14px;
  z-index: 10;
  min-width: 140px;
  text-align: center;
  animation: fadeIn 0.18s;
}
.donate-qr-pop img {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  background: #fff;
  object-fit: cover;
  margin-bottom: 6px;
  box-shadow: 0 2px 8px #7ecfff33;
  border: 2px solid #7ecfff33;
}
.donate-qr-label {
  color: #7ecfff;
  font-size: 0.98em;
  font-weight: 600;
  margin-top: 2px;
  letter-spacing: 1px;
}
.donate-qr-arrow {
                position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 18px;
  height: 10px;
  overflow: hidden;
}
.donate-qr-arrow::after {
  content: '';
  display: block;
  width: 18px;
  height: 18px;
  background: #23243a;
  border-radius: 4px;
  transform: rotate(45deg);
  position: absolute;
  left: 0;
  top: -9px;
  box-shadow: 0 2px 8px #7ecfff33;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-50%) translateY(10px);}
  to { opacity: 1; transform: translateX(-50%) translateY(0);}
}
.donate-btn-wrap:hover .donate-qr-pop,
.donate-btn:focus + .donate-qr-pop {
  display: block;
}
@media (max-width: 700px) {
  .donate-simple-card { padding: 10px 2px 8px 2px; }
  .donate-btn { font-size: 1em; padding: 8px 12px; }
  .donate-qr-pop { padding: 8px 4px 4px 4px; min-width: 90px; }
  .donate-qr-pop img { width: 60px; height: 60px; }
            }
        </style>
        <!-- 弹窗 -->
        <div class="donate-modal-mask" id="donateModalMask" style="display:none;" onclick="hideDonateModal()">
          <div class="donate-modal" onclick="event.stopPropagation()">
            <img id="donateModalQr" src="" alt="二维码" class="donate-modal-qr">
            <div class="donate-modal-type" id="donateModalType"></div>
            <div class="donate-modal-close" onclick="hideDonateModal()">×</div>
            </div>
          </div>
        <!-- 文章声明区 -->
        <section class="vh-copyright" style="margin-bottom: 15px !important;">
            <p>本文由 <span>
                    <?php echo (isset($article['author']) && ($article['author'] !== '')?$article['author']:$site['name']); ?>
                </span>
                于
                <?php echo date('Y-m-d H:i', is_numeric($article['createtime']) ? $article['createtime'] : strtotime($article['createtime'])); ?>
                发布
            </p>
            <p>文章地址：<a href="<?php echo \think\Request::instance()->url(); ?>" target="_blank">
                https://deshengdulikaifa.com<?php echo \think\Request::instance()->url(); ?>
                </a></p>
            <p>
                本博客所有文章除特别声明外，均采用
                <a href="https://creativecommons.org/licenses/by-nc-sa/4.0" target="_blank" rel="noopener nofollow">CC
                    BY-NC-SA 4.0</a>
                许可协议。完整转载请注明来自
                <a href="https://deshengdulikaifa.com" target="_blank">
                   本站
                </a>！
            </p>
            <svg class="vh-copyright-bg" viewBox="0 0 496 512" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M245.8 214.9l-33.2 17.3c-9.4-19.6-25.2-19.9-27.5-19.9-22.1 0-33.2 14.6-33.2 43.8 0 23.6 9.2 43.8 33.2 43.8 14.5 0 24.7-7.1 30.6-21.3l30.6 15.5c-6.2 11.5-25.7 39-65.1 39-22.6 0-74-10.3-74-77.1 0-58.7 43-77.1 72.6-77.1 30.7 0 52.7 12 66 35.9zm143.1 0l-32.8 17.3c-9.5-19.8-25.7-19.9-27.9-19.9-22.1 0-33.2 14.6-33.2 43.8 0 23.6 9.2 43.8 33.2 43.8 14.5 0 24.7-7.1 30.5-21.3l31 15.5c-2.1 3.8-21.4 39-65.1 39-22.7 0-74-9.9-74-77.1 0-58.7 43-77.1 72.6-77.1 30.7 0 52.6 12 65.6 35.9zM247.6 8.1C104.7 8.1 0 123.1 0 256.1c0 138.5 113.6 248 247.6 248 129.9 0 248.4-100.9 248.4-248 0-137.9-106.6-248-248.4-248zm.9 450.8c-112.5 0-203.7-93-203.7-202.8 0-105.4 85.4-203.3 203.7-203.3 112.5 0 202.8 89.5 202.8 203.3 0 121.7-99.7 202.8-202.8 202.8z">
                </path>
            </svg>
        </section>
        
        <!-- 你认为这篇文章怎么样？模块（表情投票） -->
        <div class="card mb-4" id="article-feedback-placeholder" style="margin-bottom: 15px !important;">
            <div class="card-body text-center">
                <div class="wl-reaction">
                    <div class="wl-reaction-title">你认为这篇文章怎么样？</div>
                    <ul class="wl-reaction-list">
                        <li class="wl-reaction-item" data-key="reaction_agree">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_agree.png">
                                <div class="wl-reaction-votes">{if $article.reaction_agree >
                                    99}99+{else}{$article.reaction_agree|default=0}{/if}</div>
                            </div>
                        </li>
                        <li class="wl-reaction-item" data-key="reaction_look_down">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_look_down.png">
                                <div class="wl-reaction-votes">{if $article.reaction_look_down >
                                    99}99+{else}{$article.reaction_look_down|default=0}{/if}</div>
                            </div>
                        </li>
                        <li class="wl-reaction-item" data-key="reaction_sunglasses">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_sunglasses.png">
                                <div class="wl-reaction-votes">{if $article.reaction_sunglasses >
                                    99}99+{else}{$article.reaction_sunglasses|default=0}{/if}</div>
                            </div>
                        </li>
                        <li class="wl-reaction-item" data-key="reaction_pick_nose">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_pick_nose.png">
                                <div class="wl-reaction-votes">{if $article.reaction_pick_nose >
                                    99}99+{else}{$article.reaction_pick_nose|default=0}{/if}</div>
                            </div>
                        </li>
                        <li class="wl-reaction-item" data-key="reaction_awkward">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_awkward.png">
                                <div class="wl-reaction-votes">{if $article.reaction_awkward >
                                    99}99+{else}{$article.reaction_awkward|default=0}{/if}</div>
                            </div>
                        </li>
                        <li class="wl-reaction-item" data-key="reaction_sleep">
                            <div class="wl-reaction-img">
                                <img
                                    src="/assets/img/tieba_sleep.png">
                                <div class="wl-reaction-votes">{if $article.reaction_sleep >
                                    99}99+{else}{$article.reaction_sleep|default=0}{/if}</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 文章评论区 -->
        <div class="card-body" style="padding: 0;">
        {assign name="form_title" value="发表评论"}
        {assign name="list_title" value="全部评论"}
        {assign name="article_id" value="$article.id"}
        {assign name="show_close_btn" value="false"}
        {include file="common/comment"
            type="article"
            commentList=$commentList
            total=$total
            sort=$sort
            limit=$limit
            page=$page
            article_id=$article.id
        }
      </div>

<!-- 星空风格样式 -->
<style>
/* 文章卡片样式 */
.article-card {
    background: rgba(24,26,38,0.96);
    border-radius: 16px;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 0 12px 1px #3a3a5a33;
    border: 1.5px solid rgba(255,255,255,0.08);
    color: #e0e0f0;
    overflow: hidden;
}

.article-card .card-body {
    padding: 15px;
    box-sizing: border-box;
    color: #e0e0f0;
}

/* 文章标题 */
.article-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #e0e0f0;
    margin-bottom: 20px;
    line-height: 1.4;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 文章元信息 */
.article-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 18px;
    font-size: 1.05rem;
    color: #6c757d;
    border-bottom: 1px solid rgba(255,255,255,0.1) !important;
    padding-bottom: 16px;
    margin-bottom: 20px;
}

.article-meta > span {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #8a8a8a;
}

.article-meta i {
    color: #7ecfff;
}

/* 文章摘要 */
.article-summary-block {
  background: rgba(36,38,48,0.92);
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(24,26,38,0.10);
  padding: 15px 20px;
  margin-bottom: 15px;
  color: #e0e0f0;
  font-size: 1em;
  line-height: 1.85;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  backdrop-filter: blur(4px);
}
@media (max-width: 700px) {
  .article-summary-block {
    padding: 10px 6px;
    font-size: 0.98em;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .article-summary-block::before {
    font-size: 1em;
    margin-right: 6px;
  }
}

/* 文章内容 */
.article-content {
    color: #e0e0f0;
    line-height: 1.8;
    font-size: 1.05em;
}

.article-content h1, .article-content h2, .article-content h3, 
.article-content h4, .article-content h5, .article-content h6 {
    color: #e0e0f0;
    margin-top: 24px;
    margin-bottom: 16px;
}

.article-content p {
    margin-bottom: 16px;
    color: #d0d0d0;
}

.article-content a {
    color: #7ecfff;
    text-decoration: none;
    border-bottom: 1px solid rgba(126,207,255,0.3);
    transition: all 0.2s ease;
}

.article-content a:hover {
    color: #5a9eff;
    border-bottom-color: #5a9eff;
}

.article-content code {
    background: rgba(126,207,255,0.1);
    color: #7ecfff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
}

.article-content pre {
    background: rgba(24,26,38,0.8);
    border: 1px solid rgba(255,255,255,0.1);
        border-radius: 8px;
    padding: 16px;
    overflow-x: auto;
    margin: 16px 0;
}

.article-content pre code {
    background: none;
    color: #e0e0f0;
    padding: 0;
}

.article-content blockquote {
    border-left: 4px solid #7ecfff;
    background: rgba(126,207,255,0.05);
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 0 8px 8px 0;
    color: #d0d0d0;
}

/* 文章标签 */
.article-tags {
    border-top: 1px solid rgba(255,255,255,0.1) !important;
    padding-top: 15px !important;
    margin-top: 15px !important;
    color: #8a8a8a;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.article-tags span {
    display: flex;
}

.article-tag-link {
    color: #7ecfff;
    text-decoration: none;
    margin-right: 0;
    padding: 2px 12px;
    background: rgba(126,207,255,0.1);
    border-radius: 16px;
    font-size: 0.9em;
    transition: all 0.2s ease;
    border: 1px solid rgba(126,207,255,0.2);
    white-space: nowrap;
    word-break: keep-all;
    display: inline-block;
    margin-bottom: 6px;
}

.article-tag-link:hover {
    color: #5a9eff;
    background: rgba(126,207,255,0.2);
    border-color: #5a9eff;
    transform: translateY(-1px);
}

/* 文章导航 */
.article-nav {
    background: rgba(36,38,48,0.98);
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.08);
    margin-bottom: 18px;
    overflow: hidden;
}

.article-nav .card-body {
    padding: 15px;
    box-sizing: border-box;
    color: #e0e0f0;
}

.article-nav-link {
    display: flex;
    flex-direction: column;
    min-width: 0;
    max-width: 48%;
    color: #e0e0f0;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 8px 0;
    text-decoration: none;
}

.article-nav-link:hover {
    color: #7ecfff;
    transform: translateX(4px);
}

.article-nav-label {
    font-size: 0.9em;
    color: #8a8a8a;
    margin-bottom: 4px;
}

.article-nav-title {
    font-size: 1.05em;
    color: #e0e0f0;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.article-nav-placeholder {
    flex: 1;
}

/* 分类链接 */
.article-category-link {
    color: #7ecfff;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.article-category-link:hover {
    color: #5a9eff;
    text-decoration: underline;
}

/* 打赏卡片 */
.donate-card {
    background: rgba(36,38,48,0.92);
    border-radius: 14px;
    box-shadow: 0 2px 12px 0 rgba(126,207,255,0.08), 0 0 8px 1px #3a3a5a22;
    border: 1px solid rgba(126,207,255,0.10);
    margin-bottom: 18px;
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(4px);
    max-width: 340px;
    margin-left: auto;
    margin-right: auto;
}
.donate-body-star {
    padding: 14px 4px 10px 4px;
    background: linear-gradient(135deg, rgba(24,26,38,0.92) 60%, rgba(126,207,255,0.10) 100%);
    border-radius: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.donate-title-star {
    font-size: 1em;
    font-weight: 600;
    color: #7ecfff;
    margin-bottom: 10px;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 7px;
}
.donate-title-star .fa-coffee {
    color: #f59e42;
    font-size: 1.2em;
    margin-right: 5px;
    filter: drop-shadow(0 1px 4px #f59e4280);
}
.donate-qrcodes-star {
    gap: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-end;
    margin-bottom: 0;
}
.donate-qrcode-card {
    background: rgba(24,26,38,0.85);
    border-radius: 8px;
    box-shadow: 0 1px 6px 0 rgba(126,207,255,0.08);
    border: 1px solid rgba(126,207,255,0.10);
    padding: 7px 7px 4px 7px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2px;
    transition: box-shadow 0.2s, transform 0.2s;
}
.donate-qrcode-card:hover {
    box-shadow: 0 2px 8px 0 rgba(126,207,255,0.13);
    transform: translateY(-1px) scale(1.03);
}
.donate-qrcode-img-star {
    width: 66px;
    height: 66px;
    border-radius: 5px;
    box-shadow: 0 1px 4px #7ecfff33;
    background: #fff;
    object-fit: cover;
    border: 1px solid rgba(126,207,255,0.12);
    margin-bottom: 5px;
}
.donate-paytype-star {
    margin-top: 1px;
    font-size: 0.92em;
    color: #7ecfff;
    font-weight: 500;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px #23243a66;
}
.donate-tip-star {
    margin-top: 10px;
    font-size: 0.95em;
    color: #b2bac0;
    font-weight: 400;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px #23243a66;
}

/* 版权声明 */
.vh-copyright {
    background: rgba(36,38,48,0.98);
    border-radius: 16px;
    padding: 15px;
    box-sizing: border-box;
    /* margin: 32px 0 24px 0; */
    color: #d0d0d0;
        font-size: 1em;
        line-height: 1.8;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.08);
    }

    .vh-copyright p {
    margin: 0;
}

.vh-copyright span,
.vh-copyright a {
    color: #7ecfff;
    font-weight: 600;
    text-decoration: none;
}

.vh-copyright a:hover {
    color: #5a9eff;
        text-decoration: underline;
    }

.vh-copyright-bg {
        position: absolute;
    right: 24px;
    bottom: 0;
    width: 180px;
    height: 180px;
    opacity: 0.05;
    pointer-events: none;
    z-index: 0;
    fill: #7ecfff;
}

.vh-copyright * {
    /* position: relative; */
    z-index: 1;
}

/* 表情投票 */
    #article-feedback-placeholder.card {
    background: rgba(36,38,48,0.98);
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.08);
    overflow: hidden;
}

#article-feedback-placeholder .card-body {
    padding: 15px;
    box-sizing: border-box;
    color: #e0e0f0;
    }

    .wl-reaction-title {
        font-size: 1.4rem;
        font-weight: 700;
    color: #e0e0f0;
        margin-bottom: 30px;
        letter-spacing: 1px;
        text-align: center;
    }

    .wl-reaction-list {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        gap: 20px;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .wl-reaction-item {
        cursor: pointer;
    transition: all 0.2s ease;
        position: relative;
    border-radius: 12px;
    padding: 8px 4px 0 4px;
    }

    .wl-reaction-item:hover {
    background: rgba(126,207,255,0.1);
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(126,207,255,0.2);
    }

    .wl-reaction-item.active {
    background: rgba(126,207,255,0.15);
    box-shadow: 0 4px 20px rgba(126,207,255,0.3);
    }

    .wl-reaction-img {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .wl-reaction-img img {
        width: 40px;
        height: 40px;
    transition: transform 0.2s ease;
    }

    .wl-reaction-votes {
        margin-top: -6px;
    background: rgba(24,26,38,0.9);
    border: 2px solid #7ecfff;
    color: #7ecfff;
        border-radius: 50%;
    font-size: 0.6em;
        font-weight: bold;
        min-width: 26px;
        min-height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: -10px;
        right: -10px;
    box-shadow: 0 2px 8px rgba(126,207,255,0.3), 0 0 0 2px rgba(24,26,38,0.9) inset;
        z-index: 2;
    transition: all 0.2s ease;
        user-select: none;
    }

    @keyframes votes-pop {
        0% {
            transform: scale(0.7);
            opacity: 0.2;
        }
        60% {
            transform: scale(1.18);
            opacity: 1;
        }
        100% {
            transform: scale(1);
        }
    }

    .votes-animate {
        animation: votes-pop 0.4s;
    }

    .wl-reaction-item:hover .wl-reaction-votes,
    .wl-reaction-item.active .wl-reaction-votes {
    border-color: #5a9eff;
    color: #5a9eff;
    background: rgba(126,207,255,0.1);
    box-shadow: 0 4px 16px rgba(126,207,255,0.4), 0 0 0 2px rgba(126,207,255,0.1) inset;
        transform: scale(1.12) rotate(-6deg);
    }

/* 移除无用按钮样式，避免linter错误 */

.article-nav-row {
  display: flex;
  width: 100%;
  min-height: 110px;
  border-radius: 18px;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.13);
  background: none;
}
.article-nav-half {
  width: 50%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 110px;
  background-size: cover;
  background-position: center;
  position: relative;
  transition: box-shadow 0.2s, filter 0.2s;
  cursor: pointer;
  text-decoration: none;
  color: #fff;
  filter: brightness(0.7) blur(0px);
}
.article-nav-half:before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(24,26,38,0.45);
  z-index: 1;
  pointer-events: none;
}
.article-nav-half-left {
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  margin-right: 1px;
}
.article-nav-half-right {
  border-top-right-radius: 18px;
  border-bottom-right-radius: 18px;
  margin-left: 1px;
}
.nav-half-label {
  position: relative;
  z-index: 2;
  font-size: 1.05em;
      font-weight: 500;
  margin-bottom: 8px;
  letter-spacing: 1px;
  opacity: 0.85;
}
.nav-half-title {
  position: relative;
  z-index: 2;
  font-size: 1.18em;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0,0,0,0.25);
  padding: 0 12px;
  max-width: 90%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
}
.article-nav-half:hover:not(.nav-half-disabled) {
  filter: brightness(0.95) blur(0px);
  box-shadow: 0 4px 24px 0 rgba(126,207,255,0.13);
}
.nav-half-disabled {
  cursor: not-allowed;
  filter: grayscale(0.3) brightness(0.7) blur(0px);
  opacity: 0.7;
}

/* 响应式修正 */
@media (max-width: 700px) {
  .article-nav-row {
    flex-direction: column;
    border-radius: 12px;
  }
  .article-nav-half {
    min-height: 70px;
    border-radius: 0 !important;
  }
  .article-nav-half-left {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin-right: 0;
    margin-bottom: 1px;
  }
  .article-nav-half-right {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    margin-left: 0;
    margin-top: 1px;
  }
  .nav-half-title {
    font-size: 1em;
    padding: 0 4px;
  }
}

.donate-body-star {
    padding: 14px 4px 10px 4px;
    background: linear-gradient(135deg, rgba(24,26,38,0.92) 60%, rgba(126,207,255,0.10) 100%);
    border-radius: 14px;
  display: flex;
    flex-direction: column;
  align-items: center;
    justify-content: center;
}
.donate-title-star {
    font-size: 1em;
    font-weight: 600;
    color: #7ecfff;
    margin-bottom: 10px;
    letter-spacing: 1px;
  display: flex;
  align-items: center;
    justify-content: center;
    gap: 7px;
}
.donate-title-star .fa-coffee {
    color: #f59e42;
    font-size: 1.2em;
    margin-right: 5px;
    filter: drop-shadow(0 1px 4px #f59e4280);
}
.donate-qrcodes-star {
    gap: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-end;
    margin-bottom: 0;
}
.donate-qrcode-card {
    background: rgba(24,26,38,0.85);
    border-radius: 8px;
    box-shadow: 0 1px 6px 0 rgba(126,207,255,0.08);
    border: 1px solid rgba(126,207,255,0.10);
    padding: 7px 7px 4px 7px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2px;
    transition: box-shadow 0.2s, transform 0.2s;
}
.donate-qrcode-card:hover {
    box-shadow: 0 2px 8px 0 rgba(126,207,255,0.13);
    transform: translateY(-1px) scale(1.03);
}
.donate-qrcode-img-star {
    width: 66px;
    height: 66px;
    border-radius: 5px;
    box-shadow: 0 1px 4px #7ecfff33;
    background: #fff;
    object-fit: cover;
    border: 1px solid rgba(126,207,255,0.12);
    margin-bottom: 5px;
}
.donate-paytype-star {
    margin-top: 1px;
  font-size: 0.92em;
    color: #7ecfff;
    font-weight: 500;
  letter-spacing: 1px;
    text-shadow: 0 1px 2px #23243a66;
}
.donate-tip-star {
    margin-top: 10px;
    font-size: 0.95em;
    color: #b2bac0;
    font-weight: 400;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px #23243a66;
}

/* 响应式修正 */
@media (max-width: 700px) {
    .donate-card {
        border-radius: 8px;
        max-width: 98vw;
    }
    .donate-body-star {
        padding: 8px 1px 6px 1px;
        border-radius: 8px;
    }
    .donate-title-star {
        font-size: 0.95em;
    }
    .donate-qrcodes-star {
        gap: 6px;
    }
    .donate-qrcode-card {
        padding: 4px 3px 2px 3px;
        border-radius: 5px;
    }
    .donate-qrcode-img-star {
        width: 44px;
        height: 44px;
        border-radius: 3px;
    }
    .donate-tip-star {
        margin-top: 6px;
        font-size: 0.9em;
    }
}
.donate-simple-card {
  background: rgba(36,38,48,0.92);
  border-radius: 15px;
  box-shadow: 0 2px 12px 0 rgba(126,207,255,0.08);
  /* padding: 18px 10px 14px 10px; */
  text-align: center;
  /* margin: 0 auto 18px auto; */
  /* max-width: 400px; */
}
.donate-simple-tip {
  color: #888;
  font-size: 1.08em;
  margin-bottom: 18px;
  font-weight: 500;
  letter-spacing: 1px;
}
.donate-simple-btns {
  display: flex;
  justify-content: center;
  gap: 18px;
}
.donate-btn {
  border: none;
  outline: none;
  border-radius: 8px;
  font-size: 1.12em;
  font-weight: 700;
  padding: 10px 28px;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.15s, box-shadow 0.15s;
}
.donate-btn-alipay {
  background: #1890ff;
}
.donate-btn-wechat {
  background: #1aad19;
}
.donate-btn-alipay:hover {
  background: #1177cc;
  transform: translateY(-2px) scale(1.04);
}
.donate-btn-wechat:hover {
  background: #128c13;
  transform: translateY(-2px) scale(1.04);
}
.donate-btn i.fa {
  font-size: 1.2em;
}
.donate-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(24,26,38,0.75);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.donate-modal {
  background: #23243a;
  border-radius: 14px;
  padding: 28px 24px 18px 24px;
  box-shadow: 0 4px 24px 0 rgba(126,207,255,0.18);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 220px;
}
.donate-modal-qr {
  width: 160px;
  height: 160px;
  border-radius: 10px;
  background: #fff;
  object-fit: cover;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px #7ecfff33;
  border: 2px solid #7ecfff33;
}
.donate-modal-type {
  color: #7ecfff;
  font-size: 1.08em;
  font-weight: 600;
  margin-bottom: 6px;
  letter-spacing: 1px;
}
.donate-modal-close {
  position: absolute;
  right: 12px;
  top: 8px;
  font-size: 1.5em;
  color: #aaa;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.18s;
}
.donate-modal-close:hover {
  color: #ff6b6b;
}
@media (max-width: 700px) {
  /* .donate-simple-card { padding: 10px 2px 8px 2px; } */
  .donate-btn { font-size: 1em; padding: 8px 12px; }
  .donate-modal { padding: 16px 6px 10px 6px; min-width: 0; }
  .donate-modal-qr { width: 110px; height: 110px; }
}
</style>
{/block}
{block name='script'}
<script>
    (function () {
        var articleId = "{$article.id}";
        var storageKey = 'article_reaction_' + articleId;
        var votedKey = localStorage.getItem(storageKey);
        if (votedKey) {
            var votedItem = document.querySelector('.wl-reaction-item[data-key="' + votedKey + '"]');
            if (votedItem) votedItem.classList.add('active');
        }
        document.querySelectorAll('.wl-reaction-item').forEach(function (item) {
            item.addEventListener('click', function () {
                var emojiKey = item.getAttribute('data-key');
                if (localStorage.getItem(storageKey)) return; // 已投票
                fetch('/api/article/reaction', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'article_id=' + encodeURIComponent(articleId) + '&emoji_key=' + encodeURIComponent(emojiKey)
                })
                    .then(function (res) { return res.json(); })
                    .then(function (res) {
                        if (res.code == 1) {
                            var votes = item.querySelector('.wl-reaction-votes');
                            votes.textContent = res.data.count > 99 ? '99+' : res.data.count;
                            // 触发弹跳动画
                            votes.classList.remove('votes-animate');
                            void votes.offsetWidth;
                            votes.classList.add('votes-animate');
                            item.classList.add('active');
                            localStorage.setItem(storageKey, emojiKey);
                        } else {
                            alert(res.msg || '投票失败');
                        }
                    });
            });
        });
    })();
</script>
<script>
function showDonateModal(type) {
  var qr = '';
  var label = '';
  if(type === 'alipay') {
    qr = '/assets/img/zfb.jpg'; // 支付宝二维码
    label = '支付宝扫码打赏';
  } else {
    qr = '/assets/img/wx.jpg'; // 微信二维码
    label = '微信扫码打赏';
  }
  document.getElementById('donateModalQr').src = qr;
  document.getElementById('donateModalType').innerText = label;
  document.getElementById('donateModalMask').style.display = 'flex';
}
function hideDonateModal() {
  document.getElementById('donateModalMask').style.display = 'none';
}
</script>
{/block}