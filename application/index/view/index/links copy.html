{extend name="layout/blog" /}

{block name="content"}
<div class="about-main-container">
  <!-- 关于友链 -->
  <div class="blogger-card about-card mb-4">
    <div class="about-card-title">
      关于友链
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <p>欢迎交换友链！本站致力于分享技术、记录成长，欢迎内容优质、积极向上的网站互加友链。</p>
      <ul>
        <li>请确保您的网站内容健康、无违规</li>
        <li>优先收录原创技术博客、产品站、设计类网站</li>
        <li>如需交换，请在下方填写申请表，或通过邮箱联系我</li>
      </ul>
    </div>
  </div>

  <!-- 推荐友链 -->
  <div class="blogger-card about-card mb-4">
    <div class="about-card-title">
      推荐友链
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="friend-link-list">
        {volist name="linkList" id="link"}
        <a href="{$link.url}" target="_blank" class="friend-link-card" style="text-decoration: none;">
          <img src="{$link.logo|default='/assets/img/logo_default.png'}" class="friend-link-logo" alt="{$link.name}">
          <div class="friend-link-info">
            <div class="friend-link-title">{$link.name}</div>
            <div class="friend-link-desc">{$link.description|default=''} </div>
          </div>
        </a>
        {/volist}
      </div>
    </div>
  </div>

    <!-- 收藏网站 -->
    <div class="blogger-card about-card mb-4">
      <div class="about-card-title">
        收藏网站
      </div>
      <div class="about-card-divider"></div>
      <div class="about-card-content">
        <div class="friend-link-list">
          {volist name="favoriteList" id="fav"}
          <a href="{$fav.url}" target="_blank" class="friend-link-card" style="text-decoration: none;">
            <img src="{$fav.logo|default='/assets/img/logo_default.png'}" class="friend-link-logo" alt="{$fav.name}">
            <div class="friend-link-info">
              <div class="friend-link-title">{$fav.name}</div>
              <div class="friend-link-desc">{$fav.description|default=''} </div>
            </div>
          </a>
          {/volist}
        </div>
      </div>
    </div>

  <!-- 本站信息 -->
  <div class="blogger-card about-card mb-4">
    <div class="about-card-title">
      本站信息
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="code-block">
        <div class="code-header">
          <div class="window-controls">
            <span class="control red"></span>
            <span class="control yellow"></span>
            <span class="control green"></span>
          </div>
          <span class="code-type">YAML</span>
          <div class="code-actions">
            <i class="fa fa-copy" onclick="copySiteInfo()"></i>
            <i class="fa fa-chevron-down"></i>
          </div>
        </div>
        <div class="code-content">
          <pre><code>name: 德胜独立开发
link: https://deshengdulikaifa.com
avatar: https://deshengdulikaifa.com{$site.site_logo}
descr: {$site.description|default='分享技术、记录成长'}
email: <EMAIL></code></pre>
        </div>
      </div>
    </div>
  </div>

  <!-- 申请友链 -->
  <div class="blogger-card about-card mb-4">
    <div class="about-card-title">
      申请友链
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <form class="row g-3" id="link-apply-form" method="post" enctype="multipart/form-data" action="/api/links/apply">
        <div class="col-md-6 mb-3">
          <label for="logo" class="form-label">网站 Logo 链接</label>
          <input type="url" class="form-control custom-input" id="logo" name="logo" placeholder="">
        </div>
        <div class="col-md-6 mb-3">
          <label for="name" class="form-label">网站名称</label>
          <input type="text" class="form-control custom-input" id="name" name="name" required>
        </div>
        <div class="col-md-6 mb-3">
          <label for="url" class="form-label">网站链接</label>
          <input type="url" class="form-control custom-input" id="url" name="url" required placeholder="">
        </div>
        <div class="col-md-6 mb-3">
          <label for="description" class="form-label">网站描述</label>
          <input type="text" class="form-control custom-input" id="description" name="description" maxlength="40" required>
        </div>
        <div class="col-md-6 mb-3">
          <label for="email" class="form-label">电子邮件</label>
          <input type="email" class="form-control custom-input" id="email" name="email" required placeholder="">
        </div>
        <div class="col-12">
          <button type="submit" class="btn btn-primary px-4">提交申请</button>
        </div>
      </form>
    </div>
  </div>
</div>

<style>
.about-main-container {
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden;
  box-sizing: border-box;
}
p,ul{
  margin:0;
}
.about-card {
  background: rgba(24,26,38,0.96);
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 0 12px 1px #3a3a5a33;
  border: 1.5px solid rgba(255,255,255,0.08);
  color: #e0e0f0;
  padding: 22px 18px 18px 18px;
  margin-bottom: 24px;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  width: 100%;
}
.about-card-title {
  font-size: 1em;
  font-weight: bold;
  color: #e0e0f0;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}
.about-card-divider {
  height: 1.5px;
  background: #4445;
  margin-bottom: 12px;
  border-radius: 1px;
}
.about-card-content {
  color: #6c757d;
  font-size: 1em;
  word-break: break-all;
}
.friend-link-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}
.friend-link-card {
  flex: 1 1 calc(33.333% - 20px);
  box-sizing: border-box;
  cursor: pointer;
  transition: box-shadow 0.2s;
  background: rgba(36,38,48,0.98);
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  border: 1px solid rgba(255,255,255,0.06);
  color: #e0e0f0;
  padding: 14px 16px 12px 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  position: relative;
  text-decoration: none;
}
.friend-link-card:hover {
  box-shadow: 0 4px 16px 0 rgba(80,120,200,0.13);
  border: 1.5px solid #7ecfff44;
}
.favorite-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #ffb400;
  font-size: 20px;
  cursor: pointer;
  z-index: 2;
}
@media (max-width: 768px) {
  .friend-link-card { flex-basis: calc(50% - 20px); }
}
@media (max-width: 480px) {
  .friend-link-card { flex-basis: calc(50% - 20px); }
}
@media (max-width: 360px) {
  .friend-link-card { flex-basis: 100%; }
}
.friend-link-logo {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  object-fit: cover;
  background: #fff;
  box-shadow: 0 2px 8px #0002;
  flex-shrink: 0;
  margin-right: 12px;
}
.friend-link-info {
  flex: 1;
  min-width: 0;
}
.friend-link-title {
  font-size: 1.08em;
  font-weight: bold;
  color: #e0e0f0;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.friend-link-desc {
  color: #bdbdbd;
  font-size: 0.98em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.friend-link-btn {
  color: #7ecfff;
  font-size: 1.1em;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 8px;
  background: rgba(80,120,200,0.08);
  transition: background 0.18s, color 0.18s;
  margin-left: 8px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}
.friend-link-btn:hover {
  background: #7ecfff;
  color: #222;
}
.custom-input {
  background: #f5f6fa !important;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  color: #333;
}
.custom-input:focus {
  border-color: #b3b3b3;
  outline: none;
}
.code-block {
  background: #2d2d2d;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #444;
  margin: 10px 0;
  width: 100%;
  box-sizing: border-box;
}
.code-header {
  background: #3c3c3c;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #444;
}
.window-controls {
  display: flex;
  gap: 6px;
}
.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.control.red { background: #ff5f56; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #27ca3f; }
.code-type {
  color: #b4b4b4;
  font-size: 12px;
  font-weight: 500;
}
.code-actions {
  display: flex;
  gap: 8px;
  color: #b4b4b4;
}
.code-actions i {
  cursor: pointer;
  font-size: 14px;
}
.code-actions i:hover {
  color: #fff;
}
.code-content {
  padding: 16px;
  background: #2d2d2d;
  overflow-x: auto;
}
.code-content pre {
  margin: 0;
  color: #e0e0e0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre;
  word-break: break-all;
  overflow-wrap: break-word;
}
.code-content code {
  color: #e0e0e0;
}
/* 自定义滚动条样式 */
.code-content::-webkit-scrollbar {
  height: 8px;
}
.code-content::-webkit-scrollbar-track {
  background: #1e1e1e;
  border-radius: 4px;
}
.code-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}
.code-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}
</style>
<script>
function copySiteInfo() {
  var logo = "https://deshengdulikaifa.com{$site.site_logo}";
  var title = "德胜独立开发";
  var desc = "{$site.description|default='分享技术、记录成长'}";
  var text = `name: ${title}
link: https://deshengdulikaifa.com
avatar: ${logo}
descr: ${desc}
email: <EMAIL>`;
  
  if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(function() {
          showToast('复制成功！');
      }, function() {
          fallbackCopyTextToClipboard(text);
      });
  } else {
      fallbackCopyTextToClipboard(text);
  }
}

function showToast(message) {
  // 创建提示元素
  var toast = document.createElement('div');
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 9999;
    animation: slideIn 0.3s ease;
  `;
  toast.textContent = message;
  
  // 添加动画样式
  var style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
  `;
  document.head.appendChild(style);
  
  document.body.appendChild(toast);
  
  // 3秒后自动移除
  setTimeout(function() {
    toast.style.animation = 'slideOut 0.3s ease';
    setTimeout(function() {
      document.body.removeChild(toast);
    }, 300);
  }, 2000);
}

function fallbackCopyTextToClipboard(text) {
  var textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
      document.execCommand('copy');
      showToast('复制成功！');
  } catch (err) {
      showToast('复制失败，请手动复制');
  }
  document.body.removeChild(textArea);
}
function collectSite(id) {
  alert('收藏功能开发中，id: ' + id);
  // TODO: 实现收藏逻辑，可用ajax提交到后端
}
document.addEventListener('DOMContentLoaded', function() {
  var form = document.getElementById('link-apply-form');
  if(form){
      form.addEventListener('submit', function(e){
          e.preventDefault();
          var formData = new FormData(form);
          var xhr = new XMLHttpRequest();
          xhr.open('POST', form.action, true);
          xhr.onload = function () {
              try {
                  var res = JSON.parse(xhr.responseText);
                  if(res.code == 1){
                      alert('提交成功，等待审核');
                      form.reset();
                  }else{
                      alert(res.msg || '提交失败');
                  }
              } catch (err) {
                  alert('提交失败');
              }
          };
          xhr.send(formData);
      });
  }
});
</script>
{/block}