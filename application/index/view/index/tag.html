{extend name="layout/blog" /}

{block name="content"}
<div class="about-main-container">
  <!-- 标签云 -->
  <div class="blogger-card about-card" style="margin-bottom: 0;">
    <div class="about-card-title">
        所有标签
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
      <div class="tag-cloud">
        {volist name="tags" id="tag"}
        <a href="/tagList/{$tag.id}" class="tag-item" style="text-decoration: none;">
          <span class="tag-name">#{$tag.name}</span>
          <span class="tag-count">{$tag.count}</span>
        </a>
        {/volist}
      </div>
    </div>
  </div>
</div>

<style>
.about-main-container {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

.about-card {
  background: rgba(24, 26, 38, 0.96);
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.18), 0 0 12px 1px #3a3a5a33;
  border: 1.5px solid rgba(255, 255, 255, 0.08);
  color: #e0e0f0;
  overflow: hidden;
  position: relative;
}

.about-card-title {
  font-size: 1em;
  font-weight: bold;
  color: #e0e0f0;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 15px;
  box-sizing: border-box;
  height: 45px;
}

.about-card-divider {
  height: 1.5px;
  background: #4445;
  border-radius: 1px;
}

.about-card-content {
  color: #6c757d;
  font-size: 1em;
  word-break: break-all;
  padding: 15px;
  box-sizing: border-box;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  /* justify-content: center; */
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(36, 38, 48, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #e0e0f0;
}

.tag-name {
  font-size: 14px;
  color: #e0e0f0;
}

.tag-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(126, 207, 255, 0.1);
  border: 1px solid rgba(126, 207, 255, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  color: #7ecfff;
  min-width: 20px;
  text-align: center;
  font-weight: 600;
}

.tag-item:hover {
  transform: translateY(-1px);
  background: rgba(36, 38, 48, 1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-cloud {
    gap: 12px;
  }
  
  .tag-item {
    padding: 3px 6px;
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  .tag-cloud {
    gap: 10px;
  }
  
  .tag-item {
    padding: 2px 5px;
    font-size: 0.8rem !important;
  }
}
</style>
{/block} 