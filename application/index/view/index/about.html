{extend name="layout/blog" /}

{block name="content"}
<div class="about-main-container">
  <!-- 我的故事 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      关于本站
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
        <p>本站是我的个人博客，主要用于：</p>
        <ul>
          <li>记录开发过程中的技术心得与踩坑经验</li>
          <li>分享独立产品开发的实录与思考</li>
          <li>整理高效工具、资源和学习方法</li>
          <li>偶尔写写生活感悟和成长故事</li>
        </ul>
        <p>希望这里能成为你学习、成长、交流的一个温暖角落。</p>
    </div>
  </div>

  <!-- 拥有技能 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      拥有技能
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
        <ul>
            <li><strong>前端：</strong> 熟练掌握 Vue2/Vue3、JavaScript/TypeScript、uni-app、Element-UI 等主流框架和工具</li>
            <li><strong>后端：</strong> 熟悉 FastAdmin、ThinkPHP、Node.js、MySQL、Redis 等</li>
            <li><strong>服务器：</strong> 熟悉 Linux 系统常用指令，能独立部署和维护项目</li>
            <li><strong>工具：</strong> 熟练使用 Git、VSCode、Nginx 等开发与运维工具</li>
          </ul>
    </div>
  </div>
  <!-- 免责声明 -->
  <div class="blogger-card about-card">
    <div class="about-card-title">
      免责声明
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content">
        <p>本站所有文章和资源仅供个人工作和学习记录，部分素材来自网络会表明，若不小心影响您的利益，请联系我删除，谢谢~</p>
    </div>
  </div>
  

  <!-- 打赏/赞赏 -->
  <div class="blogger-card about-card" style="margin-bottom: 0;">
    <div class="about-card-title">
      打赏/赞赏
    </div>
    <div class="about-card-divider"></div>
    <div class="about-card-content about-reward-content">
      <div class="about-reward-text">{$site.blogger_reward_text|default='如果你喜欢我的博客，欢迎打赏支持！'}</div>
      <div class="about-reward-qrs">
        <div class="about-reward-qr-item">
          <img src="/assets/img/zfb.jpg" alt="支付宝收款码" class="about-reward-qr-img">
          <div class="about-reward-qr-label">支付宝</div>
        </div>
        <div class="about-reward-qr-item">
          <img src="/assets/img/wx.jpg" alt="微信收款码" class="about-reward-qr-img">
          <div class="about-reward-qr-label">微信</div>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
  .about-reward-text{
    text-align:initial;
  }
.about-main-container {
  max-width: 100%;
  margin: 0 auto;
}
p,ul{
  margin:0;
}
.about-card {
  background: rgba(24,26,38,0.96);
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 0 12px 1px #3a3a5a33;
  border: 1.5px solid rgba(255,255,255,0.08);
  color: #e0e0f0;
  overflow: hidden;
  position: relative;
}
.about-card-title {
  font-size: 1em;
  font-weight: bold;
  color: #e0e0f0;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-sizing: border-box;
  height: 45px;
}
.about-svg-icon {
  width: 22px;
  height: 22px;
  vertical-align: middle;
  flex-shrink: 0;
}
.about-card-divider {
  height: 1.5px;
  background: #4445;
  border-radius: 1px;
}
.about-card-content {
  color: #6c757d;
  font-size: 1em;
  word-break: break-all;
  padding: 15px;
  box-sizing: border-box;
}
.about-card-content ul, .about-card-content ol {
  /* margin-left: 1.2em; */
}
.about-card-content a {
  color: #7ecfff;
  text-decoration: underline;
  transition: color 0.18s;
}
.about-card-content a:hover {
  color: #ffc04d;
}
.about-reward-content {
  text-align: center;
}
.about-reward-qrs {
  display: flex;
  /* justify-content: center; */
  gap: 32px;
  margin-top: 18px;
}
.about-reward-qr-item {
  display: flex;
  flex-direction: column;
  /* align-items: center; */
}
.about-reward-qr-img {
  width: 120px;
  height: 120px;
  /* border-radius: 12px; */
  box-shadow: 0 2px 12px #0005;
  background: #fff;
  margin-bottom: 8px;
}
.about-reward-qr-label {
  color: #bdbdbd;
  font-size: 1em;
  margin-top: 2px;
}
</style>
{/block}
 