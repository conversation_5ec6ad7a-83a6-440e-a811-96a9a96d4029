<?php

use think\Route;

// 博客首页
Route::get('/', 'index/Index/index');

// 文章详情
Route::get('detail/:id', 'index/Index/detail');

// 归档页面
Route::get('archive', 'index/Index/archive');
Route::get('archiveList/:year', 'index/Index/archive_list');

// 分类页面
Route::get('category', 'index/Index/category');
Route::get('categoryList/:id', 'index/Index/category_list');

// 标签页面
Route::get('tag', 'index/Index/tag');
Route::get('tagList/:id', 'index/Index/tag_list');

// 留言页面
Route::get('message', 'index/Index/message');

// 关于页面
Route::get('about', 'index/Index/about');

// 友链页面
Route::get('links', 'index/Index/links');

// RSS订阅
Route::get('rss.xml', 'index/Index/rss');

// sitemap.xml 站点地图
Route::get('sitemap.xml', 'index/Index/sitemap');

