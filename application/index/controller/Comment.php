<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Comment as CommentModel;
use app\common\service\CommentService;

class Comment extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $layout = '';

    /**
     * Comment模型对象
     * @var CommentModel
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new CommentModel;
    }

    /**
     * 发布评论
     */
    public function add()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $params = $this->request->post();

        // 验证评论数据
        $validation = CommentService::validateCommentData($params);
        if ($validation !== true) {
            $this->error($validation['msg']);
        }

        // 获取客户端信息
        $clientInfo = CommentService::getClientInfo($this->request);

        $data = [
            'content' => trim($params['content']),
            'nickname' => trim($params['nickname']),
            'email' => trim($params['email']),
            'website' => $params['website'] ?? '',
            'type' => $params['type'] ?? 'article',
            'article_id' => $params['article_id'] ?? 0,
            'parent_id' => $params['parent_id'] ?? 0,
            'reply_to_id' => $params['reply_to_id'] ?? 0,
            'browser' => $clientInfo['browser'],
            'device' => $clientInfo['device'],
            'ip' => $clientInfo['ip'],
            'ip_location' => $clientInfo['ip_location'],
            'status' => 'normal',
            'createtime' => time()
        ];

        $result = CommentService::createComment($data);

        if ($result) {
            $this->success('评论发布成功');
        } else {
            $this->error('评论发布失败');
        }
    }

    /**
     * 获取子评论列表（每次加载5条）
     */
    public function children()
    {
        $parentId = $this->request->param('parent_id', 0);
        $page = max(1, intval($this->request->param('page', 1)));
        $limit = 5;

        if (!$parentId) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $type = $this->request->param('type', 'message');
        $articleId = $this->request->param('article_id', 0);

        $commentModel = new \app\common\model\Comment;
        $where = [
            'c.type' => $type,
            'c.status' => 'normal',
            'c.parent_id' => $parentId
        ];
        if ($type === 'article' && $articleId) {
            $where['c.article_id'] = $articleId;
        }

        $children_count = $commentModel->alias('c')->where($where)->count();
        $offset = ($page - 1) * $limit;
        $childrenList = $commentModel->alias('c')
            ->join('fa_comment r', 'c.reply_to_id = r.id', 'LEFT')
            ->where($where)
            ->field('c.*, r.nickname as reply_to_nickname, r.content as reply_to_content, r.website as reply_to_website')
            ->order('c.createtime desc')
            ->limit($offset, $limit)
            ->select();

        foreach ($childrenList as &$item) {
            $item['is_admin'] = CommentService::isAdminEmail($item['email']);
            $item['avatar_url'] = CommentService::getAvatarUrl($item['email'], $item['nickname']);
            $item['createtime_formatted'] = date('Y-m-d H:i', $item['createtime']);
            $item['content_formatted'] = CommentService::formatCommentContent($item['content']);

            // 新增：处理回复对象
            if (!empty($item['reply_to_id']) && $item['reply_to_id'] != 0) {
                // 查询被回复的评论
                $reply = $commentModel->where('id', $item['reply_to_id'])->find();
                if ($reply) {
                    $item['reply_to_nickname'] = $reply['nickname'];
                    $item['reply_to_website'] = $reply['website'];
                    $item['reply_to_email'] = $reply['email'];
                    $item['reply_to_content'] = CommentService::formatCommentContent($reply['content']);
                } else {
                    $item['reply_to_nickname'] = '';
                    $item['reply_to_email'] = '';
                    $item['reply_to_content'] = '';
                }
            } else {
                $item['reply_to_nickname'] = '';
                $item['reply_to_email'] = '';
                $item['reply_to_content'] = '';
            }
        }
        $html = $this->fetch('common/comment_items', ['childrenList' => $childrenList]);

        // 关键：剩余未展示的子评论总数
        $loaded = $page * $limit;
        // 剩余未展示的子评论总数
        $total = max(0, $children_count - $loaded);

        return json([
            'code' => 1,
            'data' => [
                'html' => $html,
                'total' => $total,
                'page' => $page,
                'hasMore' => ($offset + $limit - 1) < $children_count
            ]
        ]);
    }

    /**
     * 获取评论列表
     */
    public function getList()
    {
        $type = $this->request->get('type', 'article');
        $articleId = $this->request->get('article_id', 0);
        $sort = $this->request->get('sort', 'desc'); // desc, asc, hot

        $where = [
            'type' => $type,
            'status' => 'normal'
        ];

        if ($type == 'article' && $articleId) {
            $where['article_id'] = $articleId;
        }

        $query = $this->model->where($where);

        // 排序
        switch ($sort) {
            case 'asc':
                $query->order('createtime asc');
                break;
            case 'hot':
                $query->order('likes desc, createtime desc');
                break;
            default:
                $query->order('createtime desc');
        }

        $comments = $query->select();

        // 构建评论树结构
        $commentTree = $this->buildCommentTree($comments);

        $this->success('获取成功', '', $commentTree);
    }

    /**
     * 构建评论树结构
     */
    private function buildCommentTree($comments, $parentId = 0)
    {
        $tree = [];
        foreach ($comments as $comment) {
            if ($comment['parent_id'] == $parentId) {
                $comment['children'] = $this->buildCommentTree($comments, $comment['id']);
                $tree[] = $comment;
            }
        }
        return $tree;
    }

    /**
     * 点赞评论
     */
    public function like()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $commentId = $this->request->post('comment_id');
        $ip = $this->request->ip();

        $result = CommentService::toggleLike($commentId, $ip);

        if ($result['code'] == 1) {
            $this->success($result['msg']);
        } else {
            $this->error($result['msg']);
        }
    }

    /**
     * 删除评论（管理员功能）
     */
    public function delete()
    {
        if ($this->request->isPost()) {
            $commentId = $this->request->post('comment_id');

            $comment = $this->model->find($commentId);
            if (!$comment) {
                $this->error('评论不存在');
            }

            $result = $comment->delete();
            if ($result) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        }
    }

    /**
     * 回复评论（管理员功能）
     */
    public function reply()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            $data = [
                'content' => $params['content'],
                'nickname' => '博主',
                'email' => '',
                'website' => '',
                'type' => $params['type'] ?? 'article',
                'article_id' => $params['article_id'] ?? 0,
                'parent_id' => $params['parent_id'],
                'reply_to_id' => $params['reply_to_id'] ?? 0,
                'status' => 'normal',
                'is_admin' => 1,
                'createtime' => time()
            ];

            $result = $this->model->save($data);

            if ($result) {
                $this->success('回复成功');
            } else {
                $this->error('回复失败');
            }
        }
    }
}