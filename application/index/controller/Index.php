<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Article;
use app\common\model\BlogCategory;
use app\common\model\Tag;
use app\common\model\Comment;
use app\common\service\CommentService;
use think\Db;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    /**
     * Article模型对象
     * @var \app\common\model\Article
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        \app\common\service\VisitLogService::record($this->request);
        $this->model = new Article;
    }

    /**
     * 首页
     */
    public function index()
    {
        $page = $this->request->param('page', 1);
        file_put_contents(ROOT_PATH . 'runtime/page_debug.log', "page=" . var_export($page, true) . PHP_EOL, FILE_APPEND);
        $limit = 10;

        // 获取文章列表，包含分类信息
        $articleList = $this->model
            ->alias('a')
            ->join('blog_category c', 'a.category_id = c.id', 'left')
            ->where('a.status', 'normal')
            ->field('a.*, c.name as category_name')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();

        // 为每篇文章获取标签信息
        foreach ($articleList as &$article) {
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }

        $blogger = config('site.blogger') ?: config('blogger');
        $site = array_merge(
            config('site'),
            [
                'article_count' => $this->model->where('status', 'normal')->count(),
                'category_count' => BlogCategory::where('status', 'normal')->count(),
                'tag_count' => Tag::where('status', 'normal')->count(),
            ]
        );

        $hotCategories = $this->getHotCategories();
        if (count($hotCategories) > 10)
            $hotCategories = array_slice($hotCategories, 0, 10);
        $hotTags = $this->getHotTags();
        if (count($hotTags) > 10)
            $hotTags = array_slice($hotTags, 0, 10);
        $recommendArticles = $this->getRecommendArticles();
        if (is_object($recommendArticles)) {
            $recommendArticles = $recommendArticles->toArray();
        }
        if (count($recommendArticles) < 5) {
            $ids = array_column($recommendArticles, 'id');
            $commentArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->limit(5 - count($recommendArticles))->select();
            if (is_object($commentArticles)) {
                $commentArticles = $commentArticles->toArray();
            }
            $recommendArticles = array_merge($recommendArticles, $commentArticles);
            $ids = array_column($recommendArticles, 'id');
            if (count($recommendArticles) < 5) {
                $likeArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->order('likes desc')->limit(5 - count($recommendArticles))->select();
                if (is_object($likeArticles)) {
                    $likeArticles = $likeArticles->toArray();
                }
                $recommendArticles = array_merge($recommendArticles, $likeArticles);
                $ids = array_column($recommendArticles, 'id');
            }
            if (count($recommendArticles) < 5) {
                $newArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->order('createtime desc')->limit(5 - count($recommendArticles))->select();
                if (is_object($newArticles)) {
                    $newArticles = $newArticles->toArray();
                }
                $recommendArticles = array_merge($recommendArticles, $newArticles);
            }
        }

        $adSidebarTop = config('site.ads.sidebar_top') ?: ['enable' => false, 'content' => ''];
        $adSidebarBottom = config('site.ads.sidebar_bottom') ?: ['enable' => false, 'content' => ''];
        $this->view->assign([
            'articleList' => $articleList,
            'page' => $page,
            'total' => $site['article_count'],
            'limit' => $limit,
            'url' => url('index'),
            'query' => '',
            'site' => $site,
            'hotCategories' => $hotCategories,
            'hotTags' => $hotTags,
            'recommendArticles' => $recommendArticles,
            'adSidebarTop' => $adSidebarTop,
            'adSidebarBottom' => $adSidebarBottom,
        ]);

        return $this->view->fetch();
    }

    /**
     * 获取博主信息
     */
    private function getBloggerInfo()
    {
        $articleCount = $this->model->where('status', 'normal')->count();
        $categoryCount = BlogCategory::where('status', 'normal')->count();
        $tagCount = Tag::where('status', 'normal')->count();

        return [
            'name' => '德胜独立开发',
            'avatar' => '/assets/img/avatar.jpg',
            'signature' => '热爱技术，专注独立开发',
            'profession' => '全栈开发工程师',
            'sideBusiness' => '技术博主',
            'announcement' => '欢迎来到我的博客，分享技术心得和独立开发经验！',
            'articleCount' => $articleCount,
            'categoryCount' => $categoryCount,
            'tagCount' => $tagCount
        ];
    }

    /**
     * 获取热门分类
     */
    private function getHotCategories()
    {
        return BlogCategory::alias('c')
            ->join('article a', 'c.id = a.category_id')
            ->where('a.status', 'normal')
            ->where('c.status', 'normal')
            ->field('c.id, c.name, count(a.id) as count')
            ->group('c.id')
            ->order('count desc')
            ->limit(4)
            ->select();
    }

    /**
     * 获取热门标签
     */
    private function getHotTags()
    {
        return Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->join('article a', 'at.article_id = a.id')
            ->where('a.status', 'normal')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(at.article_id) as count')
            ->group('t.id')
            ->order('count desc')
            ->limit(18)
            ->select();
    }

    /**
     * 获取推荐文章
     */
    private function getRecommendArticles()
    {
        return $this->model
            ->where('status', 'normal')
            ->where('is_recommend', 1)
            ->order('createtime desc')
            ->limit(5)
            ->select();
    }

    /**
     * 文章详情
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        $article = $this->model->find($id);
        if (!$article) {
            $this->error('文章不存在');
        }
        // 查询分类信息
        $category = \app\common\model\BlogCategory::where('id', $article['category_id'])->find();
        $article['category_name'] = $category ? $category['name'] : '未分类';
        // 计算时间段
        $hour = (int) date('H', $article['createtime']);
        if ($hour >= 0 && $hour < 6) {
            $article['daypart'] = '凌晨';
        } elseif ($hour >= 6 && $hour < 12) {
            $article['daypart'] = '上午';
        } elseif ($hour >= 12 && $hour < 14) {
            $article['daypart'] = '中午';
        } elseif ($hour >= 14 && $hour < 18) {
            $article['daypart'] = '下午';
        } else {
            $article['daypart'] = '晚上';
        }
        // 查询标签并补充到 $article
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->where('at.article_id', $id)
            ->where('t.status', 'normal')
            ->field('t.id, t.name')
            ->select();
        $article['tags'] = $tags;

        // 增加阅读量
        $this->model->where('id', $id)->setInc('views');

        // 上一篇（发布时间大于当前，最早的那一篇，比当前新）
        $prev = $this->model
            ->where('createtime', '>', $article['createtime'])
            ->where('status', 'normal')
            ->order('createtime', 'asc')
            ->find();

        // 下一篇（发布时间小于当前，最新的那一篇，比当前旧）
        $next = $this->model
            ->where('createtime', '<', $article['createtime'])
            ->where('status', 'normal')
            ->order('createtime', 'desc')
            ->find();

        // 评论区分页参数
        $page = $this->request->param('page', 1);
        $limit = 10;
        $sort = $this->request->param('sort', 'desc');
        // 排序逻辑
        $orderMap = [
            'asc' => 'createtime asc',
            'hot' => 'likes desc, createtime desc',
            'desc' => 'createtime desc'
        ];
        $order = $orderMap[$sort] ?? 'createtime desc';
        $commentModel = new Comment;
        $where = ['type' => 'article', 'status' => 'normal', 'parent_id' => 0, 'article_id' => $id];
        $total = $commentModel->where($where)->count();
        $commentList = $commentModel
            ->where($where)
            ->order($order)
            ->page($page, $limit)
            ->select();

        // 处理留言数据，并为每个父评论加载一个子评论
        foreach ($commentList as &$item) {
            $item['is_admin'] = CommentService::isAdminEmail($item['email']);
            $item['avatar_url'] = CommentService::getAvatarUrl($item['email'], $item['nickname']);
            $item['createtime_formatted'] = date('Y-m-d H:i', $item['createtime']);
            $item['content_formatted'] = CommentService::formatCommentContent($item['content']);

            // 获取子评论数量
            $item['children_count'] = $commentModel->where([
                'type' => 'article',
                'status' => 'normal',
                'parent_id' => $item['id']
            ])->count();

            $item['children'] = null;
        }

        // 新增：处理AJAX请求，返回 JSON 格式评论数据，结构与留言板一致
        if ($this->request->param('ajax')) {
            // 与message方法保持一致，渲染HTML片段
            $html = $this->fetch('common/comment_list', [
                'commentList' => $commentList,
                'page' => $page,
                'total' => $total,
                'limit' => $limit,
                'sort' => $sort,
                'type' => 'article',
                'urlss' => url('detail', ['id' => $id]),
                'article_id' => $id
            ]);
            return json([
                'html' => $html,
                'commentList' => collection($commentList)->toArray(),
                'total' => $total
            ]);
        }
        $this->view->assign([
            'article' => $article,
            'commentList' => $commentList,
            'total' => $total,
            'sort' => $sort,
            'limit' => $limit,
            'page' => $page,
            'prev' => $prev,
            'next' => $next,
            'type' => 'article',
        ]);

        return $this->view->fetch();
    }

    /**
     * 归档页面
     */
    public function archive()
    {
        // 查询所有年份及每年文章总数
        $years = $this->model
            ->where('status', 'normal')
            ->field('YEAR(FROM_UNIXTIME(createtime)) as year, COUNT(*) as count')
            ->group('year')
            ->order('year desc')
            ->select();
        $total = $this->model->where('status', 'normal')->count();
        $this->view->assign([
            'archives' => $years,
            'total' => $total,
            'limit' => 10,
            'page' => 1,
            'url' => url('archive'),
            'query' => ''
        ]);
        return $this->view->fetch();
    }

    /**
     * 归档年份文章列表
     */
    public function archive_list($year = null)
    {
        if (!$year) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->where('status', 'normal')
            ->whereTime('createtime', 'between', [strtotime("$year-01-01 00:00:00"), strtotime("$year-12-31 23:59:59")])
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->where('status', 'normal')
            ->whereTime('createtime', 'between', [strtotime("$year-01-01 00:00:00"), strtotime("$year-12-31 23:59:59")])
            ->count();
        // 为每篇文章补充分类和标签信息
        foreach ($articles as &$article) {
            $category = BlogCategory::where('id', $article['category_id'])->find();
            $article['category_name'] = $category ? $category['name'] : '未分类';
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }
        $this->view->assign([
            'year' => $year,
            'articles' => $articles,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('archive_list', ['year' => $year]),
            'query' => ''
        ]);
        return $this->view->fetch('archive_list');
    }

    /**
     * 分类页面
     */
    public function category($id = null)
    {
        if ($id) {
            $articles = $this->model
                ->where('category_id', $id)
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        } else {
            $articles = $this->model
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        }

        // 获取所有分类及每个分类下的文章数
        $categories = BlogCategory::where('status', 'normal')->order('sort desc,id desc')->select();
        foreach ($categories as &$cat) {
            $cat['article_count'] = $this->model->where('category_id', $cat['id'])->where('status', 'normal')->count();
        }
        $this->view->assign([
            'articles' => $articles,
            'categories' => $categories,
            'currentCategory' => $id
        ]);

        return $this->view->fetch();
    }

    /**
     * 标签页面
     */
    public function tag($id = null)
    {
        if ($id) {
            $articles = $this->model
                ->alias('a')
                ->join('article_tag at', 'a.id = at.article_id')
                ->where('at.tag_id', $id)
                ->where('a.status', 'normal')
                ->order('a.createtime desc')
                ->select();
        } else {
            $articles = $this->model
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        }

        // 获取所有标签及其文章数，按count降序
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id', 'left')
            ->join('article a', 'at.article_id = a.id', 'left')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(a.id) as count')
            ->group('t.id')
            ->order('count desc')
            ->select();
        // 计算最大最小count
        $tags = $tags ? (is_object($tags) ? $tags->toArray() : $tags) : [];
        $minFont = 1.0;
        $maxFont = 2.2;
        $minCount = null;
        $maxCount = null;
        foreach ($tags as $tag) {
            if ($minCount === null || $tag['count'] < $minCount)
                $minCount = $tag['count'];
            if ($maxCount === null || $tag['count'] > $maxCount)
                $maxCount = $tag['count'];
        }
        $range = $maxCount - $minCount > 0 ? $maxCount - $minCount : 1;
        foreach ($tags as &$tag) {
            $size = $minFont + ($tag['count'] - $minCount) / $range * ($maxFont - $minFont);
            $size = max($minFont, min($size, $maxFont));
            $color = ($tag['count'] == $maxCount) ? '#007bff' : (($tag['count'] == $minCount) ? '#6c757d' : '#17a2b8');
            $tag['font_size'] = $size;
            $tag['color'] = $color;
        }
        // 打乱标签顺序
        shuffle($tags);
        $this->view->assign([
            'articles' => $articles,
            'tags' => $tags,
            'currentTag' => $id
        ]);

        return $this->view->fetch();
    }

    /**
     * 标签文章列表页面
     */
    public function tag_list($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->alias('a')
            ->join('article_tag at', 'a.id = at.article_id')
            ->where('at.tag_id', $id)
            ->where('a.status', 'normal')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->alias('a')
            ->join('article_tag at', 'a.id = at.article_id')
            ->where('at.tag_id', $id)
            ->where('a.status', 'normal')
            ->count();
        $tag = Tag::where('id', $id)->find();
        // 标签云数据
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id', 'left')
            ->join('article a', 'at.article_id = a.id', 'left')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(a.id) as count')
            ->group('t.id')
            ->select();
        // 计算最大最小count
        $tags = $tags ? (is_object($tags) ? $tags->toArray() : $tags) : [];
        $minFont = 1.0;
        $maxFont = 2.2;
        $minCount = null;
        $maxCount = null;
        foreach ($tags as $t) {
            if ($minCount === null || $t['count'] < $minCount)
                $minCount = $t['count'];
            if ($maxCount === null || $t['count'] > $maxCount)
                $maxCount = $t['count'];
        }
        $range = $maxCount - $minCount > 0 ? $maxCount - $minCount : 1;
        foreach ($tags as &$t) {
            $size = $minFont + ($t['count'] - $minCount) / $range * ($maxFont - $minFont);
            $size = max($minFont, min($size, $maxFont));
            $color = ($t['count'] == $maxCount) ? '#007bff' : (($t['count'] == $minCount) ? '#6c757d' : '#17a2b8');
            $t['font_size'] = $size;
            $t['color'] = $color;
        }
        shuffle($tags);
        // 为每篇文章补充分类名
        foreach ($articles as &$article) {
            $category = BlogCategory::where('id', $article['category_id'])->find();
            $article['category_name'] = $category ? $category['name'] : '未分类';
            if (isset($article['article_id'])) {
                $article['id'] = $article['article_id'];
            }
        }
        $this->view->assign([
            'articles' => $articles,
            'currentTag' => $tag,
            'tags' => $tags,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('tag_list', ['id' => $id]),
            'query' => ''
        ]);
        return $this->view->fetch('tag_list');
    }

    /**
     * 留言页面
     */
    public function message()
    {
        $page = $this->request->param('page', 1);
        $limit = 10;
        $sort = $this->request->param('sort', 'desc');

        // 排序逻辑
        $orderMap = [
            'asc' => 'createtime asc',
            'hot' => 'likes desc, createtime desc',
            'desc' => 'createtime desc'
        ];
        $order = $orderMap[$sort] ?? 'createtime desc';

        // 获取父评论列表（parent_id = 0）
        $commentModel = new Comment;
        $where = ['type' => 'message', 'status' => 'normal', 'parent_id' => 0];

        $total = $commentModel->where($where)->count();
        $commentList = $commentModel
            ->where($where)
            ->order($order)
            ->page($page, $limit)
            ->select();

        // 处理留言数据
        foreach ($commentList as &$item) {
            $item['is_admin'] = CommentService::isAdminEmail($item['email']);
            $item['avatar_url'] = CommentService::getAvatarUrl($item['email'], $item['nickname']);
            $item['createtime_formatted'] = date('Y-m-d H:i', $item['createtime']);
            $item['content_formatted'] = CommentService::formatCommentContent($item['content']);
            // 获取子评论数量
            $item['children_count'] = $commentModel->where([
                'type' => 'message',
                'status' => 'normal',
                'parent_id' => $item['id']
            ])->count();
            $item['children'] = null;
        }


        // 处理AJAX请求
        if ($this->request->param('ajax')) {
            $html = $this->fetch('common/comment_list', [
                'commentList' => $commentList,
                'page' => $page,
                'total' => $total,
                'limit' => $limit,
                'sort' => $sort,
                'type' => 'message',
            ]);
            return json([
                'html' => $html
            ]);
        }

        $this->view->assign([
            'commentList' => $commentList,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'sort' => $sort,
            'url' => url('message'),
            'query' => '',
            'type' => 'message',
        ]);

        return $this->view->fetch();
    }

    /**
     * 获取子评论列表
     */
    public function getChildrenComments()
    {
        $parentId = $this->request->param('parent_id', 0);
        $page = $this->request->param('page', 1);
        $limit = 10;

        if (!$parentId) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $commentModel = new Comment;
        $where = [
            'c.type' => 'message',
            'c.status' => 'normal',
            'c.parent_id' => $parentId
        ];

        $total = $commentModel->alias('c')->where($where)->count();
        $childrenList = $commentModel->alias('c')
            ->join('fa_comment r', 'c.reply_to_id = r.id', 'LEFT')
            ->where($where)
            ->field('c.*, r.nickname as reply_to_nickname, r.content as reply_to_content, r.website as reply_to_website')
            ->order('c.createtime desc')
            ->page($page, $limit)
            ->select();

        // 处理子评论数据
        foreach ($childrenList as &$item) {
            $item['is_admin'] = CommentService::isAdminEmail($item['email']);
            $item['avatar_url'] = CommentService::getAvatarUrl($item['email'], $item['nickname']);
            $item['createtime_formatted'] = date('Y-m-d H:i', $item['createtime']);
            $item['content_formatted'] = CommentService::formatCommentContent($item['content']);
        }

        return json([
            'code' => 1,
            'data' => [
                'childrenList' => collection($childrenList)->toArray(),
                'total' => $total,
                'page' => $page,
                'hasMore' => ($page * $limit) < $total
            ]
        ]);
    }

    /**
     * 留言点赞
     */
    public function message_like($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        $comment = Comment::get($id);
        if (!$comment || $comment['type'] !== 'message') {
            $this->error('留言不存在');
        }

        $comment->setInc('likes');
        $this->success('点赞成功', url('message', $this->request->get()));
    }

    /**
     * 关于页面
     */
    public function about()
    {
        return $this->view->fetch();
    }





    /**
     * 友链页面
     */
    public function links()
    {
        $favoriteList = \think\Db::name('links')->where(['status' => 1, 'type' => 'favorite'])->order('id desc')->select();
        $linkList = \think\Db::name('links')->where(['status' => 1, 'type' => 'friend'])->order('id desc')->select();
        $this->assign('favoriteList', $favoriteList);
        $this->assign('linkList', $linkList);
        return $this->view->fetch();
    }

    /**
     * Banner数据
     */
    public function bannerData()
    {
        return [
            'title' => '德胜独立开发',
            'subtitle' => '热爱技术，专注独立开发',
            'description' => '分享技术心得和独立开发经验',
            'image' => '/assets/img/banner.jpg'
        ];
    }

    /**
     * 标签云数据
     */
    public function tagcloud()
    {
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->join('article a', 'at.article_id = a.id')
            ->where('a.status', 'normal')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(at.article_id) as count')
            ->group('t.id')
            ->order('count desc')
            ->select();

        return json($tags);
    }

    /**
     * 分类文章列表页面
     */
    public function category_list($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->alias('a')
            ->where('a.category_id', $id)
            ->where('a.status', 'normal')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->alias('a')
            ->where('a.category_id', $id)
            ->where('a.status', 'normal')
            ->count();
        $category = BlogCategory::where('id', $id)->find();
        // 为每篇文章补充标签信息
        foreach ($articles as &$article) {
            $article['category_name'] = $category ? $category['name'] : '未分类';
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }
        $this->view->assign([
            'articles' => $articles,
            'category' => $category,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('category_list', ['id' => $id]),
            'query' => ''
        ]);
        return $this->view->fetch('category_list');
    }

    /**
     * RSS订阅源
     */
    public function rss()
    {
        header('Content-Type: application/rss+xml; charset=utf-8');
        $siteTitle = '德胜独立开发';
        $siteLink = 'https://deshengdulikaifa.com/';
        $siteDesc = '德胜独立开发，专注独立产品、技术分享与成长记录。';
        $articles = \app\common\model\Article::where('status', 'normal')->order('createtime desc')->limit(20)->select();

        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        echo "<rss version=\"2.0\">\n<channel>\n";
        echo "<title>{$siteTitle}</title>\n";
        echo "<link>{$siteLink}</link>\n";
        echo "<description>{$siteDesc}</description>\n";
        echo "<language>zh-cn</language>\n";
        echo "<lastBuildDate>" . date('r') . "</lastBuildDate>\n";
        foreach ($articles as $a) {
            $url = $siteLink . 'detail/' . $a['id'];
            $title = htmlspecialchars($a['title']);
            $desc = htmlspecialchars(strip_tags($a['summary'] ?: mb_substr($a['content'], 0, 120)));
            $date = date('r', $a['createtime']);
            echo "<item>\n";
            echo "<title>{$title}</title>\n";
            echo "<link>{$url}</link>\n";
            echo "<description><![CDATA[{$desc}]]></description>\n";
            echo "<pubDate>{$date}</pubDate>\n";
            echo "<guid>{$url}</guid>\n";
            echo "</item>\n";
        }
        echo "</channel>\n</rss>";
        exit;
    }

    /**
     * sitemap.xml 站点地图
     */
    public function sitemap()
    {
        header('Content-Type: application/xml; charset=utf-8');
        $siteUrl = 'https://deshengdulikaifa.com/';
        $now = date('Y-m-d');

        // 获取数据
        $articles = \app\common\model\Article::where('status', 'normal')->order('createtime desc')->limit(100)->select();
        $categories = \app\common\model\BlogCategory::where('status', 'normal')->select();
        $tags = \app\common\model\Tag::where('status', 'normal')->select();

        // 获取文章年份用于归档页面
        $years = \app\common\model\Article::where('status', 'normal')
            ->field('YEAR(FROM_UNIXTIME(createtime)) as year')
            ->group('year')
            ->order('year desc')
            ->select();

        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

        // 首页
        echo "  <url>\n    <loc>{$siteUrl}</loc>\n    <lastmod>{$now}</lastmod>\n    <changefreq>daily</changefreq>\n    <priority>1.0</priority>\n  </url>\n";

        // 固定页面
        $staticPages = [
            'archive' => ['changefreq' => 'weekly', 'priority' => '0.8'],
            'category' => ['changefreq' => 'weekly', 'priority' => '0.8'],
            'tag' => ['changefreq' => 'weekly', 'priority' => '0.8'],
            'message' => ['changefreq' => 'monthly', 'priority' => '0.6'],
            'about' => ['changefreq' => 'monthly', 'priority' => '0.6'],
            'links' => ['changefreq' => 'weekly', 'priority' => '0.7']
        ];

        foreach ($staticPages as $page => $config) {
            $pageUrl = $siteUrl . $page;
            echo "  <url>\n    <loc>{$pageUrl}</loc>\n    <lastmod>{$now}</lastmod>\n    <changefreq>{$config['changefreq']}</changefreq>\n    <priority>{$config['priority']}</priority>\n  </url>\n";
        }

        // 分类列表页
        foreach ($categories as $cat) {
            $catUrl = $siteUrl . 'categoryList/' . $cat['id'];
            echo "  <url>\n    <loc>{$catUrl}</loc>\n    <lastmod>{$now}</lastmod>\n    <changefreq>weekly</changefreq>\n    <priority>0.7</priority>\n  </url>\n";
        }

        // 标签列表页
        foreach ($tags as $tag) {
            $tagUrl = $siteUrl . 'tagList/' . $tag['id'];
            echo "  <url>\n    <loc>{$tagUrl}</loc>\n    <lastmod>{$now}</lastmod>\n    <changefreq>weekly</changefreq>\n    <priority>0.7</priority>\n  </url>\n";
        }

        // 归档年份页面
        foreach ($years as $year) {
            $archiveUrl = $siteUrl . 'archiveList/' . $year['year'];
            echo "  <url>\n    <loc>{$archiveUrl}</loc>\n    <lastmod>{$now}</lastmod>\n    <changefreq>monthly</changefreq>\n    <priority>0.6</priority>\n  </url>\n";
        }

        // 文章详情页
        foreach ($articles as $a) {
            $url = $siteUrl . 'detail/' . $a['id'];
            $lastmod = date('Y-m-d', $a['createtime']);
            echo "  <url>\n    <loc>{$url}</loc>\n    <lastmod>{$lastmod}</lastmod>\n    <changefreq>monthly</changefreq>\n    <priority>0.6</priority>\n  </url>\n";
        }

        echo "</urlset>\n";
        exit;
    }
}
