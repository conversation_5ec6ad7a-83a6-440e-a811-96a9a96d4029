<?php
namespace app\index\controller;

use think\Controller;
use think\Request;
use app\common\model\Article;
use app\common\model\BlogCategory;
use app\common\model\Tag;

class Search extends Controller
{
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '', 'trim');
        $page = $request->get('page', 1, 'intval');
        $limit = $request->get('limit', 10, 'intval');
        $isAjax = $request->get('ajax', 0, 'intval'); // 是否是AJAX请求（用于加载更多）
        
        $list = [];
        $total = 0;
        
        if ($keyword) {
            // 构建权重搜索查询
            $query = Article::alias('a')
                ->join('blog_category c', 'a.category_id = c.id', 'LEFT')
                ->join('article_tag at', 'a.id = at.article_id', 'LEFT')
                ->join('tag t', 'at.tag_id = t.id', 'LEFT')
                ->where('a.status', 'normal')
                ->where(function($query) use ($keyword) {
                    // 标题匹配 - 最高权重
                    $query->where('a.title', 'like', "%{$keyword}%")
                          // 内容匹配 - 第二权重
                          ->whereOr('a.content', 'like', "%{$keyword}%")
                          // 分类匹配 - 第三权重
                          ->whereOr('c.name', 'like', "%{$keyword}%")
                          // 标签匹配 - 第四权重
                          ->whereOr('t.name', 'like', "%{$keyword}%");
                })
                ->field('a.*, c.name as category_name')
                ->group('a.id')
                ->order('a.createtime desc');

            // 计算总数
            $total = $query->count();
            
            // 分页查询
            $list = $query->page($page, $limit)->select();
            
            // 处理搜索结果，添加权重分数和匹配字段
            foreach ($list as &$item) {
                $score = 0;
                $matchedFields = [];
                
                // 计算权重分数
                if (stripos($item['title'], $keyword) !== false) {
                    $score += 100; // 标题权重最高
                    $matchedFields[] = 'title';
                }
                if (stripos($item['content'], $keyword) !== false) {
                    $score += 50; // 内容权重
                    $matchedFields[] = 'content';
                }
                // 获取真实分类名
                $category = \app\common\model\BlogCategory::where('id', $item['category_id'])->find();
                $item['category_name'] = $category ? $category['name'] : '未分类';
                if (isset($item['category_name']) && stripos($item['category_name'], $keyword) !== false) {
                    $score += 30; // 分类权重
                    $matchedFields[] = 'category';
                }
                // 获取真实标签
                $tags = \app\common\model\Tag::alias('t')
                    ->join('article_tag at', 't.id = at.tag_id')
                    ->where('at.article_id', $item['id'])
                    ->where('t.status', 'normal')
                    ->field('t.id, t.name')
                    ->select();
                $item['tags'] = $tags;
                foreach ($tags as $tag) {
                    if (stripos($tag['name'], $keyword) !== false) {
                        $score += 20; // 标签权重
                        $matchedFields[] = 'tag';
                        break;
                    }
                }
                
                $item['score'] = $score;
                $item['matched_fields'] = array_unique($matchedFields);
                $item['content_preview'] = mb_substr(strip_tags($item['content']), 0, 100, 'utf-8') . '...';
                $item['createtime_text'] = date('Y-m-d', $item['createtime']);
            }
            
            // 按权重分数排序
            usort($list, function($a, $b) {
                return $b['score'] - $a['score'];
            });
        }
        
        // 计算分页信息
        $hasMore = ($page * $limit) < $total;
        $totalPages = ceil($total / $limit);
        
        // 如果是AJAX请求（加载更多），只返回结果列表
        if ($isAjax) {
            $html = '';
            foreach ($list as $item) {
                $html .= $this->renderSearchItem($item);
            }
            return json(['html' => $html, 'hasMore' => $hasMore, 'page' => $page]);
        }
        
        $this->assign([
            'list' => $list,
            'keyword' => $keyword,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'hasMore' => $hasMore,
            'totalPages' => $totalPages
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 渲染单个搜索结果项
     */
    private function renderSearchItem($item)
    {
        $matchedFields = $item['matched_fields'] ?? [];
        $matchedBadges = '';
        
        if (!empty($matchedFields)) {
            $fieldNames = [
                'title' => '标题',
                'content' => '内容', 
                'category' => '分类',
                'tag' => '标签'
            ];
            
            $badges = [];
            foreach ($matchedFields as $field) {
                if (isset($fieldNames[$field])) {
                    $badges[] = '<span class="matched-badge">' . $fieldNames[$field] . '</span>';
                }
            }
            $matchedBadges = '<div class="search-result-matched">' . implode('', $badges) . '</div>';
        }
        
        return '
        <div class="search-result-item">
            <a href="' . url('article', ['id' => $item['id']]) . '" class="search-result-title" target="_blank">
                ' . htmlspecialchars($item['title']) . '
            </a>
            <div class="search-result-content">
                ' . htmlspecialchars($item['content_preview']) . '
            </div>
            <div class="search-result-meta">
                <div class="d-flex align-items-center">
                    <span class="search-result-category">' . htmlspecialchars($item['category_name']) . '</span>
                    <span class="search-result-score ml-2">权重: ' . $item['score'] . '</span>
                </div>
                <span>' . $item['createtime_text'] . '</span>
            </div>
            ' . $matchedBadges . '
        </div>';
    }


} 