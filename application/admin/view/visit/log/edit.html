<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Visit_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-visit_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[visit_time]" type="text" value="{$row.visit_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-url" data-rule="required" class="form-control" name="row[url]" type="text" value="{$row.url|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Referer')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-referer" class="form-control" name="row[referer]" type="text" value="{$row.referer|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ip')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ip" data-rule="required" class="form-control" name="row[ip]" type="text" value="{$row.ip|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ip_location')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ip_location" class="form-control" name="row[ip_location]" type="text" value="{$row.ip_location|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_agent')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_agent" class="form-control" name="row[user_agent]" type="text" value="{$row.user_agent|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Os')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-os" class="form-control" name="row[os]" type="text" value="{$row.os|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Browser')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-browser" class="form-control" name="row[browser]" type="text" value="{$row.browser|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel" class="form-control" name="row[channel]" type="text" value="{$row.channel|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Utm_source')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-utm_source" class="form-control" name="row[utm_source]" type="text" value="{$row.utm_source|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
