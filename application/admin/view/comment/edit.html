<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">编辑评论</h3>
    </div>
    <div class="panel-body">
        <!-- 评论上下文信息 -->
        <div class="alert alert-info">
            <h4><i class="fa fa-info-circle"></i> 评论信息</h4>
            <div class="row">
                <div class="col-md-4">
                    <strong>评论ID：</strong>{$row.id}
                </div>
                <div class="col-md-4">
                    <strong>评论类型：</strong>
                    {switch name="row.type"}
                        {case value="article"}文章评论{/case}
                        {case value="message"}留言{/case}
                        {case value="about"}关于页面{/case}
                        {case value="links"}友链页面{/case}
                        {default /}未知
                    {/switch}
                </div>
                <div class="col-md-4">
                    <strong>发布时间：</strong>{$row.createtime|date="Y-m-d H:i:s",###}
                </div>
            </div>

            <!-- 显示父评论信息 -->
            {if isset($parent)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>父评论：</strong>
                    <div class="well well-sm" style="margin-top: 5px; background-color: #f9f9f9; color: #333; border: 1px solid #ddd;">
                        <small class="text-muted">@{$parent.nickname|htmlentities} ({$parent.createtime|date="Y-m-d H:i",###})：</small><br>
                        <div style="color: #333;">{$parent.content|nl2br|htmlentities}</div>
                    </div>
                </div>
            </div>
            {/if}

            <!-- 显示被回复的评论信息 -->
            {if isset($replyTo)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>回复的评论：</strong>
                    <div class="well well-sm" style="margin-top: 5px; background-color: #fff3cd; color: #333; border: 1px solid #ddd;">
                        <small class="text-muted">@{$replyTo.nickname|htmlentities} ({$replyTo.createtime|date="Y-m-d H:i",###})：</small><br>
                        <div style="color: #333;">{$replyTo.content|nl2br|htmlentities}</div>
                    </div>
                </div>
            </div>
            {/if}

            <!-- 显示关联文章信息 -->
            {if isset($article)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>关联文章：</strong>
                    <span class="text-primary">{$article.title|htmlentities}</span>
                </div>
            </div>
            {/if}
        </div>

        <form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
            <!-- 评论内容 -->
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <textarea id="c-content" data-rule="required" class="form-control" rows="6" name="row[content]">{$row.content|htmlentities}</textarea>
                </div>
            </div>
            <!-- 用户信息（仅非博主评论可编辑） -->
            {if $row.is_admin != 1}
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <input id="c-email" class="form-control" name="row[email]" type="text" value="{$row.email|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Website')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <input id="c-website" class="form-control" name="row[website]" type="text" value="{$row.website|htmlentities}">
                </div>
            </div>
            {else /}
            <!-- 博主评论显示只读信息 -->
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">用户信息:</label>
                <div class="col-xs-12 col-sm-10">
                    <div class="form-control-static">
                        <span class="label label-success">博主</span>
                        {$row.nickname|htmlentities}
                        {if $row.email}({$row.email|htmlentities}){/if}
                    </div>
                </div>
            </div>
            {/if}

            <!-- 只读信息显示 -->
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">评论统计:</label>
                <div class="col-xs-12 col-sm-10">
                    <div class="form-control-static">
                        <span class="label label-info">点赞数: {$row.likes|default=0}</span>
                        {if $row.ip}
                        <span class="label label-default">IP: {$row.ip|htmlentities}</span>
                        {/if}
                        {if $row.ip_location}
                        <span class="label label-default">{$row.ip_location|htmlentities}</span>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 技术信息 -->
            {if $row.browser || $row.device}
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">设备信息:</label>
                <div class="col-xs-12 col-sm-10">
                    <div class="form-control-static">
                        {if $row.browser}<span class="label label-default">{$row.browser|htmlentities}</span>{/if}
                        {if $row.device}<span class="label label-default">{$row.device|htmlentities}</span>{/if}
                    </div>
                </div>
            </div>
            {/if}
            <!-- 状态设置 -->
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <div class="radio">
                    {foreach name="statusList" item="vo"}
                    <label for="row[status]-{$key}">
                        <input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} />
                        {$vo}
                    </label>
                    {/foreach}
                    </div>
                    <div class="help-block">
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i>
                            隐藏的评论在前台不会显示，但管理员仍可在后台查看
                        </small>
                    </div>
                </div>
            </div>

            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-10">
                    <button type="submit" class="btn btn-primary btn-embossed">
                        <i class="fa fa-save"></i> {:__('OK')}
                    </button>
                    <button type="button" class="btn btn-default" onclick="parent.Layer.closeAll();">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* 确保评论内容可见性 */
.well {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    padding: 10px !important;
    border-radius: 4px !important;
}

.well * {
    color: #333 !important;
}

.text-muted {
    color: #777 !important;
}

/* 表单样式 */
.form-control {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ccc !important;
}

.form-control:focus {
    border-color: #66afe9 !important;
    box-shadow: 0 1px 1px rgba(0,0,0,.075) inset, 0 0 8px rgba(102,175,233,.6) !important;
}

.form-control-static .label {
    margin-right: 5px;
}

.alert .well {
    margin-bottom: 0;
}

.help-block small {
    color: #999 !important;
}

/* 按钮样式 */
.btn-primary {
    background-color: #337ab7 !important;
    border-color: #2e6da4 !important;
    color: #fff !important;
}

.btn-primary:hover {
    background-color: #286090 !important;
    border-color: #204d74 !important;
}

/* 标签样式 */
.label-success {
    background-color: #5cb85c !important;
    color: #fff !important;
}

/* 面板样式 */
.panel-body {
    background-color: #fff !important;
    color: #333 !important;
}

.alert-info {
    background-color: #d9edf7 !important;
    border-color: #bce8f1 !important;
    color: #31708f !important;
}
</style>
