<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('comment/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('comment/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-info btn-reply btn-disabled disabled {:$auth->check('comment/reply')?'':'hide'}" title="博主回复" ><i class="fa fa-reply"></i> 博主回复</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('comment/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        

                        <div class="dropdown btn-group {:$auth->check('comment/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                {foreach name="statusList" item="vo"}
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:" data-params="status={$key}">{:__('Set status to ' . $key)}</a></li>
                                {/foreach}
                            </ul>
                        </div>

                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('comment/edit')}"
                           data-operate-del="{:$auth->check('comment/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<style>
/* 树形评论列表样式 */
.comment-tree-content {
    display: inline-block;
    vertical-align: top;
    max-width: 250px;
}

.comment-tree-content .content-text {
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    word-break: break-word;
    white-space: normal;
}

/* 用户信息样式 */
.user-info {
    max-width: 140px;
}

.user-info .nickname {
    font-weight: bold;
    color: #333;
    font-size: 13px;
}

.user-info .email {
    color: #666;
    font-size: 11px;
    word-break: break-all;
    margin-top: 2px;
}

.user-info .label {
    font-size: 10px;
    margin-left: 3px;
    vertical-align: middle;
}

/* 表格行样式 */
#table tbody tr {
    height: auto;
    min-height: 45px;
}

#table tbody td {
    vertical-align: middle;
    padding: 6px 8px;
    border-bottom: 1px solid #f0f0f0;
}

/* 树形结构的父评论行 */
#table tbody tr[data-parent-id="null"] {
    background-color: #fafafa;
    border-left: 4px solid #5cb85c;
    font-weight: 500;
}

/* 树形结构的子评论行 */
#table tbody tr[data-parent-id]:not([data-parent-id="null"]) {
    background-color: #f9f9f9;
    border-left: 4px solid #d9edf7;
}

/* 树形展开/折叠图标 */
.tree-toggle {
    transition: all 0.2s ease;
    font-size: 14px;
}

.tree-toggle:hover {
    color: #23527c !important;
    transform: scale(1.1);
}

/* 树形层级连接线 */
#table tbody tr[data-level="1"] td:first-child::before {
    content: '';
    position: absolute;
    left: 25px;
    top: 0;
    bottom: 50%;
    width: 1px;
    background-color: #ddd;
}

#table tbody tr[data-level="1"] td:first-child::after {
    content: '';
    position: absolute;
    left: 25px;
    top: 50%;
    width: 15px;
    height: 1px;
    background-color: #ddd;
}

#table tbody tr[data-level="1"] td:first-child {
    position: relative;
}

/* 树形缩进样式 */
.tree-indent {
    display: inline-block;
    width: 20px;
    text-align: center;
}

.tree-indent .fa-reply {
    color: #999;
    font-size: 12px;
}

/* 操作按钮样式 */
.btn-xs {
    margin-right: 2px;
    padding: 2px 6px;
    font-size: 11px;
}

/* 状态标签样式 */
.label {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
}

/* 类型标签颜色 */
.label-primary { background-color: #337ab7; }
.label-info { background-color: #5bc0de; }
.label-warning { background-color: #f0ad4e; }
.label-success { background-color: #5cb85c; }
.label-danger { background-color: #d9534f; }

/* 点赞显示 */
.text-danger .fa-heart {
    color: #e74c3c;
}

/* 搜索框样式 */
.fixed-table-toolbar .search input {
    width: 200px;
}

/* 工具栏按钮间距 */
.toolbar .btn {
    margin-right: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .comment-tree-content,
    .user-info {
        max-width: none;
    }

    #table tbody td {
        padding: 4px 6px;
        font-size: 12px;
    }

    .btn-xs {
        padding: 1px 4px;
        font-size: 10px;
    }
}

/* 表格头部样式 */
.fixed-table-header th {
    background-color: #f5f5f5;
    font-weight: bold;
    border-bottom: 2px solid #ddd;
}

/* 悬停效果 */
#table tbody tr:hover {
    background-color: #f0f8ff !important;
}

/* 选中行样式 */
#table tbody tr.selected {
    background-color: #d9edf7 !important;
}
</style>
