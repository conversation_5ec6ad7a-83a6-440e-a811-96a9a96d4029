<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">博主回复评论</h3>
    </div>
    <div class="panel-body">
        <!-- 原评论信息 -->
        <div class="alert alert-info">
            <h4><i class="fa fa-comment"></i> 原评论信息</h4>
            <div class="row">
                <div class="col-md-6">
                    <strong>用户：</strong>{$row.nickname|htmlentities}
                    {if $row.is_admin == 1}<span class="label label-success">博主</span>{/if}
                </div>
                <div class="col-md-6">
                    <strong>时间：</strong>{$row.createtime|date="Y-m-d H:i:s",###}
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>内容：</strong>
                    <div class="well well-sm" style="margin-top: 5px; background-color: #f5f5f5; color: #333; border: 1px solid #ddd;">
                        {$row.content|nl2br|htmlentities}
                    </div>
                </div>
            </div>
            
            <!-- 显示父评论信息（如果是子评论） -->
            {if isset($parent)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>父评论：</strong>
                    <div class="well well-sm" style="margin-top: 5px; background-color: #f9f9f9; color: #333; border: 1px solid #ddd;">
                        <small class="text-muted">@{$parent.nickname|htmlentities}：</small>
                        <div style="color: #333;">{$parent.content|nl2br|htmlentities}</div>
                    </div>
                </div>
            </div>
            {/if}
            
            <!-- 显示被回复的评论信息 -->
            {if isset($replyTo)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>回复的评论：</strong>
                    <div class="well well-sm" style="margin-top: 5px; background-color: #fff3cd; color: #333; border: 1px solid #ddd;">
                        <small class="text-muted">@{$replyTo.nickname|htmlentities}：</small>
                        <div style="color: #333;">{$replyTo.content|nl2br|htmlentities}</div>
                    </div>
                </div>
            </div>
            {/if}
            
            <!-- 显示关联文章信息 -->
            {if isset($article)}
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-12">
                    <strong>关联文章：</strong>
                    <span class="text-primary">{$article.title|htmlentities}</span>
                </div>
            </div>
            {/if}
        </div>

        <!-- 回复表单 -->
        <form id="reply-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
            {:token()}
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">博主回复：</label>
                <div class="col-xs-12 col-sm-10">
                    <textarea id="c-content" data-rule="required" class="form-control" rows="6" name="row[content]" placeholder="请输入回复内容..."></textarea>
                    <div class="help-block">
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i> 
                            回复将以"博主"身份发布，并自动标记为管理员回复
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-10">
                    <button type="submit" class="btn btn-primary btn-embossed">
                        <i class="fa fa-reply"></i> 发布回复
                    </button>
                    <button type="button" class="btn btn-default" onclick="parent.Layer.closeAll();">
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* 确保评论内容可见性 */
.well {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
    padding: 10px !important;
    border-radius: 4px !important;
}

.well * {
    color: #333 !important;
}

.text-muted {
    color: #777 !important;
}

/* 表单样式 */
.form-control {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ccc !important;
}

.form-control:focus {
    border-color: #66afe9 !important;
    box-shadow: 0 1px 1px rgba(0,0,0,.075) inset, 0 0 8px rgba(102,175,233,.6) !important;
}

/* 按钮样式 */
.btn-primary {
    background-color: #337ab7 !important;
    border-color: #2e6da4 !important;
    color: #fff !important;
}

.btn-primary:hover {
    background-color: #286090 !important;
    border-color: #204d74 !important;
}

/* 标签样式 */
.label-success {
    background-color: #5cb85c !important;
    color: #fff !important;
}

/* 面板样式 */
.panel-body {
    background-color: #fff !important;
    color: #333 !important;
}

.alert-info {
    background-color: #d9edf7 !important;
    border-color: #bce8f1 !important;
    color: #31708f !important;
}
</style>


