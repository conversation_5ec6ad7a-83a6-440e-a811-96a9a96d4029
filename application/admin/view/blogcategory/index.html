<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del')}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('blogcategory/edit')}" 
                           data-operate-del="{:$auth->check('blogcategory/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var Table = $("#table").bootstrapTable({
        url: 'blogcategory/index',
        pk: 'id',
        sortName: 'sort',
        columns: [
            [
                {checkbox: true},
                {field: 'id', title: __('Id'), sortable: true},
                {field: 'name', title: __('Name'), align: 'left'},
                {field: 'description', title: __('Description'), align: 'left'},
                {field: 'sort', title: __('Sort'), sortable: true},
                {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                {field: 'createtime', title: __('Createtime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                {field: 'updatetime', title: __('Updatetime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                {field: 'operate', title: __('Operate'), table: Table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
            ]
        ]
    });
</script> 