<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\admin\library\traits\Backend as BackendTrait;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 友链管理
 *
 * @icon fa fa-circle-o
 */
class Links extends Backend
{
    use BackendTrait;

    /**
     * Links模型对象
     * @var \app\admin\model\Links
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Links;
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if (!$this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 记录原始状态
        $oldStatus = $row['status'];
        $newStatus = $params['status'] ?? $oldStatus;

        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if (false === $result) {
            $this->error(__('No rows were updated'));
        }

        // 检查状态是否发生变化，如果是则发送邮件通知
        if ($oldStatus != $newStatus && in_array($newStatus, [1, 2]) && !empty($row['email'])) {
            $this->sendLinkNotificationEmail($row, $newStatus, $params);
        }

        $this->success();
    }

    /**
     * 发送友链审核结果邮件通知
     *
     * @param $linkData 友链数据
     * @param $status 新状态 1=通过 2=拒绝
     * @param $params 更新参数
     */
    protected function sendLinkNotificationEmail($linkData, $status, $params)
    {
        try {
            $email = new \app\common\library\Email();

            switch ($status) {
                case 1:
                    // 通过审核
                    $subject = '友链申请通过通知 - 德胜独立开发';
                    $template = 'link_approved';
                    $templateData = [
                        'name' => $linkData['name'],
                        'url' => $linkData['url'],
                        'description' => $linkData['description']
                    ];
                    break;
                case 2:
                    // 拒绝申请
                    $subject = '友链申请未通过通知 - 德胜独立开发';
                    $template = 'link_rejected';
                    $templateData = [
                        'name' => $linkData['name'],
                        'url' => $linkData['url'],
                        'description' => $linkData['description'],
                        'reject_reason' => $params['reject_reason'] ?? '未提供具体原因'
                    ];
                    break;
                default:
                    return; // 不支持的状态，直接返回
            }

            // 渲染邮件模板（创建独立的视图实例，避免继承后台布局）
            $view = new \think\View();
            $view->config([
                'view_path' => APP_PATH . 'admin/view/',
                'layout_on' => false,  // 禁用布局
                'layout_name' => '',
            ]);
            $html = $view->fetch('mail/' . $template, $templateData);

            // 发送邮件
            $email->to($linkData['email'])
                  ->subject($subject)
                  ->message($html)
                  ->send();

            // 记录日志
            \think\Log::info('友链审核邮件发送成功: ' . $linkData['email'] . ' 状态: ' . ($status == 1 ? '通过' : '拒绝'));

        } catch (Exception $e) {
            // 邮件发送失败，记录日志但不影响主流程
            \think\Log::error('友链审核邮件发送失败: ' . $e->getMessage());
        }
    }
}
