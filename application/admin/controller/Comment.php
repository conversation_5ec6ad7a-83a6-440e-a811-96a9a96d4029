<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\admin\library\traits\Backend as BackendTrait;
use app\common\service\CommentService;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 评论管理
 *
 * @icon fa fa-circle-o
 */
class Comment extends Backend
{
    use BackendTrait;

    /**
     * Comment模型对象
     * @var \app\admin\model\Comment
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Comment;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看评论列表（树形结构）
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        // 获取查询参数
        $search = $this->request->get("search", '');
        $filter = $this->request->get("filter", '');
        $sort = $this->request->get("sort", 'createtime');
        $order = $this->request->get("order", "DESC");

        // 构建查询条件
        $where = [];
        if ($search) {
            $where[] = ['content|nickname|email', 'LIKE', "%{$search}%"];
        }

        // 处理筛选条件
        if ($filter) {
            $filter = (array)json_decode($filter, true);
            foreach ($filter as $k => $v) {
                if (!empty($v)) {
                    $where[] = [$k, '=', $v];
                }
            }
        }

        // 获取所有评论数据（用于构建树形结构）
        $allComments = $this->model
            ->alias('c')
            ->join('fa_article a', 'c.article_id = a.id', 'LEFT')
            ->field('c.*, a.title as article_title')
            ->where($where)
            ->order($sort, $order)
            ->select();

        // 构建树形数据
        $treeData = $this->buildCommentTree($allComments);

        $result = ['total' => count($treeData), 'rows' => $treeData];
        return json($result);
    }

    /**
     * 构建评论树形数据
     */
    private function buildCommentTree($comments)
    {
        $tree = [];
        $lookup = [];

        // 确保 $comments 是数组
        if (!is_array($comments)) {
            $comments = $comments->toArray();
        }

        // 先处理所有评论，建立索引
        foreach ($comments as $comment) {
            // 确保 $comment 是数组
            if (is_object($comment)) {
                $comment = $comment->toArray();
            }
            $comment = $this->formatCommentData($comment);
            $lookup[$comment['id']] = $comment;
        }

        // 构建树形结构
        foreach ($lookup as $comment) {
            if ($comment['parent_id'] == 0) {
                // 父评论
                $comment['_level'] = 0;
                $comment['_parent_id'] = null;
                $comment['_expanded'] = true; // 默认展开
                $tree[] = $comment;

                // 添加子评论
                $this->addChildrenToTree($tree, $comment, $lookup, 1);
            }
        }

        return $tree;
    }

    /**
     * 递归添加子评论到树中
     */
    private function addChildrenToTree(&$tree, $parent, $lookup, $level)
    {
        foreach ($lookup as $comment) {
            if ($comment['parent_id'] == $parent['id']) {
                $comment['_level'] = $level;
                $comment['_parent_id'] = $parent['id'];
                $comment['_expanded'] = true;

                // 添加层级缩进标识
                $comment['_indent'] = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);

                $tree[] = $comment;

                // 递归添加更深层的子评论
                $this->addChildrenToTree($tree, $comment, $lookup, $level + 1);
            }
        }
    }

    /**
     * 格式化评论数据
     */
    private function formatCommentData($comment)
    {
        // 确保数据安全性
        $comment['content'] = $comment['content'] ?? '';
        $comment['nickname'] = $comment['nickname'] ?? '匿名用户';
        $comment['is_admin'] = intval($comment['is_admin'] ?? 0);
        $comment['parent_id'] = intval($comment['parent_id'] ?? 0);
        $comment['type'] = $comment['type'] ?? 'article';
        $comment['createtime'] = $comment['createtime'] ?? time();

        // 内容预览
        $content = strip_tags($comment['content']);
        $comment['content_preview'] = mb_substr($content, 0, 80);
        if (mb_strlen($content) > 80) {
            $comment['content_preview'] .= '...';
        }

        // 博主标识
        $comment['admin_badge'] = $comment['is_admin'] ? '<span class="label label-success">博主</span>' : '';

        // 用户信息格式化
        $comment['user_display'] = $comment['nickname'];
        if ($comment['is_admin']) {
            $comment['user_display'] .= ' <span class="label label-success">博主</span>';
        }

        // 评论类型显示
        $typeMap = [
            'article' => '文章评论',
            'message' => '留言',
            'about' => '关于页面',
            'links' => '友链页面'
        ];
        $comment['type_display'] = $typeMap[$comment['type']] ?? $comment['type'];

        // 关联内容显示
        $comment['related_display'] = ($comment['type'] === 'article' && !empty($comment['article_title']))
            ? $comment['article_title']
            : $comment['type_display'];

        // 时间格式化
        $comment['createtime_display'] = date('Y-m-d H:i:s', $comment['createtime']);

        return $comment;
    }

    /**
     * 博主回复评论
     */
    public function reply($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if (!$this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params) || empty($params['content'])) {
            $this->error(__('回复内容不能为空'));
        }

        // 获取客户端信息（使用与前台相同的服务）
        $clientInfo = CommentService::getClientInfo($this->request);

        // 构建回复数据
        $replyData = [
            'content' => trim($params['content']),
            'nickname' => '德胜独立开发',
            'email' => '<EMAIL>', // 博主邮箱
            'website' => '',
            'type' => $row['type'],
            'article_id' => $row['article_id'],
            'parent_id' => $row['parent_id'] > 0 ? $row['parent_id'] : $row['id'], // 如果是回复子评论，parent_id保持不变
            'reply_to_id' => $row['id'], // 回复的是当前评论
            'is_admin' => 1,
            'status' => 'normal',
            'browser' => $clientInfo['browser'],
            'device' => $clientInfo['device'],
            'ip' => $clientInfo['ip'],
            'ip_location' => $clientInfo['ip_location'],
            'createtime' => time()
        ];

        $result = false;
        Db::startTrans();
        try {
            $result = $this->model->save($replyData);
            if ($result) {
                // 发送邮件通知
                $this->sendReplyNotification($row, $replyData);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if (false === $result) {
            $this->error(__('回复失败'));
        }

        $this->success('回复成功');
    }

    /**
     * 编辑评论（限制可编辑字段）
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if (!$this->request->isPost()) {
            // 获取关联信息用于显示
            if ($row['parent_id'] > 0) {
                $parent = $this->model->get($row['parent_id']);
                $this->view->assign('parent', $parent);
            }
            if ($row['reply_to_id'] > 0) {
                $replyTo = $this->model->get($row['reply_to_id']);
                $this->view->assign('replyTo', $replyTo);
            }
            if ($row['article_id'] > 0) {
                $article = Db::name('article')->where('id', $row['article_id'])->find();
                $this->view->assign('article', $article);
            }

            $this->view->assign('row', $row);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 只允许编辑特定字段
        $allowedFields = ['content', 'status'];
        if ($row['is_admin'] != 1) {
            // 非博主评论还可以编辑昵称和邮箱
            $allowedFields = array_merge($allowedFields, ['nickname', 'email', 'website']);
        }

        $updateData = [];
        foreach ($allowedFields as $field) {
            if (isset($params[$field])) {
                $updateData[$field] = $params[$field];
            }
        }

        $result = false;
        Db::startTrans();
        try {
            $result = $row->allowField($allowedFields)->save($updateData);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if (false === $result) {
            $this->error(__('No rows were updated'));
        }

        $this->success();
    }



    /**
     * 发送回复通知邮件（使用前台相同的邮件系统）
     */
    private function sendReplyNotification($originalComment, $replyData)
    {
        try {
            // 只有当原评论有邮箱且不是博主自己的评论时才发送邮件
            if (empty($originalComment['email']) || $originalComment['is_admin'] == 1) {
                return;
            }

            // 获取文章信息
            $article = null;
            $article_title = '';
            $article_url = '';

            if ($originalComment['type'] === 'article' && $originalComment['article_id']) {
                $article = Db::name('article')->where('id', $originalComment['article_id'])->find();
                if ($article) {
                    $article_title = $article['title'];
                    $article_url = $this->request->domain() . '/detail/' . $article['id'];
                }
            } else {
                // 其他类型的评论
                $typeMap = [
                    'message' => '留言板',
                    'about' => '关于页面',
                    'links' => '友链页面'
                ];
                $article_title = $typeMap[$originalComment['type']] ?? '博客';
                $article_url = $this->request->domain();
            }

            // 构建邮件数据（使用前台相同的数据结构）
            $mailData = [
                'title' => '您的评论收到了博主回复',
                'subtitle' => '德胜独立开发 回复了您的评论',
                'original_nickname' => $originalComment['nickname'], // 原评论者昵称
                'nickname' => $originalComment['nickname'] . ' 同学，您曾发表评论：',
                'content' => $originalComment['content'],
                'article_title' => '查看文章',
                'article_url' => $article_url,
                'blogger_name' => '德胜独立开发博客',
                'parent_nickname' => $replyData['nickname'], // 博主昵称
                'parent_content' => $replyData['content']    // 博主回复内容
            ];

            // 使用前台相同的邮件模板和发送方式
            $this->sendEmailUsingTemplate($originalComment['email'], $mailData);

        } catch (Exception $e) {
            // 邮件发送失败不影响回复功能
            \think\Log::error('博主回复邮件发送失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用前台相同的邮件模板发送邮件
     */
    private function sendEmailUsingTemplate($to, $mailData)
    {
        try {
            // 使用前台相同的Email类
            $email = new \app\common\library\Email();

            // 渲染邮件模板（创建独立的视图实例，避免继承后台布局）
            $view = new \think\View();
            $view->config([
                'view_path' => APP_PATH . 'api/view/',
                'layout_on' => false,  // 禁用布局
                'layout_name' => '',
            ]);
            $html = $view->fetch('mail/notify_blogger', $mailData);

            // 发送邮件
            $result = $email
                ->to($to)
                ->subject($mailData['title'])
                ->message($html)
                ->send();

            if ($result) {
                \think\Log::info('博主回复邮件发送成功: ' . $to);
                return true;
            } else {
                \think\Log::error('博主回复邮件发送失败: ' . $email->getError() . ' 收件人: ' . $to);
                return false;
            }

        } catch (\Exception $e) {
            // 邮件发送失败，记录日志但不影响主流程
            \think\Log::error('博主回复邮件发送异常: ' . $e->getMessage() . ' 收件人: ' . $to);
            return false;
        }
    }
}
