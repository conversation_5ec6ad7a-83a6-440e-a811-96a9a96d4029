<?php

namespace app\admin\model;

use think\Model;


class Tag extends Model
{

    

    

    // 表名
    protected $name = 'tag';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    // 关联文章
    public function articles()
    {
        return $this->belongsToMany('Article', 'article_tag', 'article_id', 'tag_id');
    }




}
