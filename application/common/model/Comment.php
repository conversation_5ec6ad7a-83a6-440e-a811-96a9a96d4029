<?php

namespace app\common\model;

use think\Model;

class Comment extends Model
{
    // 表名
    protected $name = 'comment';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'type_text',
        'createtime_text',
        'updatetime_text',
        'avatar',
        'createtime_formatted',
        'content_formatted',
        'children_count' // 新增
    ];
    
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    public function getTypeList()
    {
        return [
            'article' => '文章评论',
            'message' => '留言',
            'about' => '关于页面',
            'links' => '友链页面'
        ];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['type'];
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setCreatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUpdatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['createtime']) ? $data['createtime'] : '');
        return (is_numeric($value) && $value > 0) ? date("Y-m-d H:i:s", $value) : '';
    }

    public function getUpdatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['updatetime']) ? $data['updatetime'] : '');
        return (is_numeric($value) && $value > 0) ? date("Y-m-d H:i:s", $value) : '';
    }

    /**
     * 获取头像
     * @param   string  $value
     * @param   array   $data
     * @return string
     */
    public function getAvatarAttr($value, $data)
    {
        $email = !empty($data['email']) ? $data['email'] : '<EMAIL>';
        $hash = md5(strtolower(trim($email)));
        // 使用 cravatar.cn 国内镜像，并指定 d=identicon 作为默认头像样式
        return "https://cravatar.cn/avatar/" . $hash . "?d=identicon";
    }

    /**
     * 获取格式化的创建时间（Y-m-d H:i）
     */
    public function getCreatetimeFormattedAttr($value, $data)
    {
        $time = $value ?: (isset($data['createtime']) ? $data['createtime'] : 0);
        return (is_numeric($time) && $time > 0) ? date('Y-m-d H:i', $time) : '';
    }

    /**
     * 获取格式化后的评论内容（支持 HTML 转义、换行等）
     */
    public function getContentFormattedAttr($value, $data)
    {
        $content = $value ?: (isset($data['content']) ? $data['content'] : '');
        return nl2br(htmlspecialchars($content));
    }

    /**
     * 获取子评论数量
     */
    public function getChildrenCountAttr($value, $data)
    {
        if ($value !== null) {
            return $value;
        }
        return self::where('parent_id', $data['id'])->count();
    }

    // 关联文章
    public function article()
    {
        return $this->belongsTo('Article', 'article_id', 'id');
    }

    // 关联父评论
    public function parent()
    {
        return $this->belongsTo('Comment', 'parent_id', 'id');
    }

    // 关联子评论
    public function children()
    {
        return $this->hasMany('Comment', 'parent_id', 'id');
    }

    // 关联被回复的评论
    public function replyTo()
    {
        return $this->belongsTo('Comment', 'reply_to_id', 'id');
    }
} 