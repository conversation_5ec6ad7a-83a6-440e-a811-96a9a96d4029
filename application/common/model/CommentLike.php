<?php

namespace app\common\model;

use think\Model;

class CommentLike extends Model
{
    // 表名
    protected $name = 'comment_like';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    // 追加属性
    protected $append = [
        'createtime_text'
    ];

    protected function setCreatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['createtime']) ? $data['createtime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    // 关联评论
    public function comment()
    {
        return $this->belongsTo('Comment', 'comment_id', 'id');
    }
} 