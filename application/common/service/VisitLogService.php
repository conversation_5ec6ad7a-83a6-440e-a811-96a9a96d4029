<?php
namespace app\common\service;
use app\common\service\CommentService;
class VisitLogService
{
    public static function record($request)
    {
            // 获取客户端信息
            $clientInfo = CommentService::getClientInfo($request);

        // 复用 CommentService 的方法
        $ip = $clientInfo['ip'];
        $ipLocation = $clientInfo['ip_location'];
        $userAgent = $request->header('user-agent');
        $referer = $request->header('referer', '');
        $url = $request->url(true);
        $os = $clientInfo['device'];
        $browser = $clientInfo['browser'];
        $channel = self::parseChannel($referer);
        $utmSource = $request->param('utm_source', '');

        db('visit_log')->insert([
            'visit_time' => date('Y-m-d H:i:s'),
            'url'        => $url,
            'referer'    => $referer,
            'ip'         => $ip,
            'ip_location'=> $ipLocation,
            'user_agent' => $userAgent,
            'os'         => $os,
            'browser'    => $browser,
            'channel'    => $channel,
            'utm_source' => $utmSource,
        ]);
    }

    public static function parseChannel($referer) {
        if (!$referer) return 'Direct';
        if (stripos($referer, 'baidu.com') !== false) return 'Baidu';
        if (stripos($referer, 'google.') !== false) return 'Google';
        if (stripos($referer, 'so.com') !== false) return '360';
        if (stripos($referer, 'sogou.com') !== false) return 'Sogou';
        if (stripos($referer, 'bing.com') !== false) return 'Bing';
        if (stripos($referer, 'weixin') !== false || stripos($referer, 'wechat') !== false) return 'WeChat';
        return 'Other';
    }
}