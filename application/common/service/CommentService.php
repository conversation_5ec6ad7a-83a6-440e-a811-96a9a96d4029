<?php

namespace app\common\service;

use app\common\model\Comment;
use app\common\model\CommentLike;

/**
 * 评论服务类
 * 统一处理评论相关的业务逻辑
 */
class CommentService
{
    /**
     * 验证评论数据
     * @param array $data
     * @return array|true
     */
    public static function validateCommentData($data)
    {
        // 验证必填字段
        if (empty($data['nickname'])) {
            return ['code' => 0, 'msg' => '昵称不能为空'];
        }
        
        if (empty($data['email'])) {
            return ['code' => 0, 'msg' => '邮箱不能为空'];
        }
        
        if (empty($data['content'])) {
            return ['code' => 0, 'msg' => '评论内容不能为空'];
        }
        
        // 验证邮箱格式
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return ['code' => 0, 'msg' => '邮箱格式不正确'];
        }
        
        // 验证内容长度
        if (mb_strlen($data['content']) > 1000) {
            return ['code' => 0, 'msg' => '评论内容不能超过1000字符'];
        }
        
        return true;
    }
    
    /**
 * 获取客户端信息
 * @param object $request
 * @return array
 */
public static function getClientInfo($request)
{
    $ip = $request->ip();
    $userAgent = $request->server('HTTP_USER_AGENT');
    
    // IP归属地
    $ip_location = self::getIpLocationByApi($ip);
    
    // 浏览器识别（带版本号）
    $browser = 'Unknown';
    $browserVersion = '';
    
    // Chrome
    if (preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches)) {
        $browser = 'Chrome';
        $browserVersion = $matches[1];
    } 
    // Firefox
    elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches)) {
        $browser = 'Firefox';
        $browserVersion = $matches[1];
    } 
    // Safari (不是Chrome)
    elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent, $matches) && !strpos($userAgent, 'Chrome')) {
        $browser = 'Safari';
        if (preg_match('/Version\/([0-9.]+)/', $userAgent, $versionMatches)) {
            $browserVersion = $versionMatches[1];
        }
    } 
    // Edge
    elseif (preg_match('/Edg(e)?\/([0-9.]+)/', $userAgent, $matches)) {
        $browser = 'Edge';
        $browserVersion = $matches[2];
    } 
    // Opera
    elseif (preg_match('/OPR\/([0-9.]+)/', $userAgent, $matches)) {
        $browser = 'Opera';
        $browserVersion = $matches[1];
    } 
    // IE
    elseif (preg_match('/MSIE ([0-9.]+)/', $userAgent, $matches) || preg_match('/Trident.*rv:([0-9.]+)/', $userAgent, $matches)) {
        $browser = 'IE';
        $browserVersion = $matches[1];
    }
    
    // 设备和操作系统识别
    $device = 'Unknown';
    $os = 'Unknown';
    $osVersion = '';
    
    // Windows
    if (preg_match('/Windows NT ([0-9.]+)/', $userAgent, $matches)) {
        $os = 'Windows';
        $windowsVersions = [
            '10.0' => '10/11',
            '6.3' => '8.1',
            '6.2' => '8',
            '6.1' => '7',
            '6.0' => 'Vista',
            '5.2' => 'XP x64',
            '5.1' => 'XP',
            '5.0' => '2000'
        ];
        $osVersion = $windowsVersions[$matches[1]] ?? $matches[1];
        $device = 'PC';
    } 
    // macOS
    elseif (preg_match('/Macintosh;.*Mac OS X ([0-9_\.]+)/', $userAgent, $matches)) {
        $os = 'macOS';
        $osVersion = str_replace('_', '.', $matches[1]);
        $device = 'Mac';
    } 
    // iOS (iPhone)
    elseif (preg_match('/iPhone;.*OS ([0-9_]+)/', $userAgent, $matches)) {
        $os = 'iOS';
        $osVersion = str_replace('_', '.', $matches[1]);
        $device = 'iPhone';
    } 
    // iOS (iPad)
    elseif (preg_match('/iPad;.*OS ([0-9_]+)/', $userAgent, $matches)) {
        $os = 'iOS';
        $osVersion = str_replace('_', '.', $matches[1]);
        $device = 'iPad';
    } 
    // Android
    elseif (preg_match('/Android ([0-9\.]+)/', $userAgent, $matches)) {
        $os = 'Android';
        $osVersion = $matches[1];
        
        if (preg_match('/Mobile/', $userAgent)) {
            $device = 'Android Phone';
        } else {
            $device = 'Android Tablet';
        }
        
        // 尝试获取具体设备型号
        if (preg_match('/\((.+?)\)/', $userAgent, $modelMatches)) {
            $modelInfo = $modelMatches[1];
            $modelParts = explode(';', $modelInfo);
            foreach ($modelParts as $part) {
                if (strpos($part, 'Build/') !== false) {
                    $deviceModel = trim(explode('Build/', $part)[0]);
                    if ($deviceModel) {
                        $device = $deviceModel;
                        break;
                    }
                }
            }
        }
    } 
    // Linux
    elseif (preg_match('/Linux/', $userAgent)) {
        $os = 'Linux';
        if (preg_match('/Ubuntu/', $userAgent)) {
            $os = 'Ubuntu';
        } elseif (preg_match('/Fedora/', $userAgent)) {
            $os = 'Fedora';
        }
        $device = 'PC';
    }
    
    // 组合设备信息
    $deviceInfo = $device;
    if ($os != 'Unknown') {
        $deviceInfo = $os;
        if ($osVersion) {
            $deviceInfo .= ' ' . $osVersion;
        }
    }
    
    // 组合浏览器信息
    $browserInfo = $browser;
    if ($browserVersion) {
        $browserInfo .= ' ' . $browserVersion;
    }
    
    return [
        'ip' => $ip,
        'ip_location' => $ip_location,
        'browser' => $browserInfo,
        'device' => $deviceInfo
    ];
}
    
    /**
     * 创建评论
     * @param array $data
     * @return bool
     */
    public static function createComment($data)
    {
        $comment = new Comment();
        return $comment->save($data);
    }
    
    /**
     * 点赞评论
     * @param int $commentId
     * @param string $ip
     * @return array
     */
    public static function toggleLike($commentId, $ip)
    {
        if (!$commentId) {
            return ['code' => 0, 'msg' => '参数错误'];
        }
        // $comment = Comment::find($commentId);
        $comment = (new Comment())->find($commentId);
        // $comment = Comment::where('id', $commentId)->find();
        if (!$comment) {
            return ['code' => 0, 'msg' => '评论不存在'];
        }
        $comment->setInc('likes', 1);
    $comment->refresh(); // 获取最新数据
    return ['code' => 1, 'msg' => '点赞成功', 'likes' => $comment];
        // 检查是否已经点赞
        // $likeRecord = CommentLike::where([
        //     'comment_id' => $commentId,
        //     'ip' => $ip
        // ])->find();
        
        // if ($likeRecord) {
        //     // 取消点赞
        //     $likeRecord->delete();
        //     Comment::where('id', $commentId)->setDec('likes');
        //     return ['code' => 1, 'msg' => '取消点赞成功'];
        // } else {
            // 添加点赞
            // CommentLike::create([
            //     'comment_id' => $commentId,
            //     'ip' => $ip,
            //     'createtime' => time()
            // ]);
            // Comment::where('id', $commentId)->setInc('likes');
            // return ['code' => 1, 'msg' => '点赞成功'];
        // }
    }
    
    /**
     * 获取头像URL
     * @param string $email
     * @param string $nickname
     * @return string
     */
    public static function getAvatarUrl($email, $nickname)
    {
        $email = strtolower(trim($email));
        
        // QQ邮箱头像
        if (preg_match('/^([1-9][0-9]{4,10})@qq\.com$/', $email, $m)) {
            return 'https://q1.qlogo.cn/g?b=qq&nk=' . $m[1] . '&s=100';
        }
        
        // 生成字母头像
        $letter = strtoupper(mb_substr($nickname ?: $email, 0, 1, 'utf-8'));
        $hash = 0;
        for ($i = 0; $i < strlen($email); $i++) {
            $hash = ord($email[$i]) + (($hash << 5) - $hash);
        }
        
        $color = '#';
        for ($i = 0; $i < 3; $i++) {
            $value = ($hash >> ($i * 8)) & 0xFF;
            $color .= str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
        }
        
        $svg = '<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg">' .
               '<circle cx="24" cy="24" r="24" fill="' . $color . '"/>' .
               '<text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" ' .
               'font-family="Arial,Helvetica,sans-serif" dy=".1em">' . $letter . '</text></svg>';
               
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
    
    /**
     * 检查是否为管理员邮箱
     * @param string $email
     * @return bool
     */
    public static function isAdminEmail($email)
    {
        $adminEmails = ['<EMAIL>']; // 可配置的管理员邮箱列表
        return in_array(strtolower(trim($email)), $adminEmails);
    }
    
    /**
     * 格式化评论内容
     * @param string $content
     * @return string
     */
    public static function formatCommentContent($content)
    {
        // 先转义HTML特殊字符
        $content = htmlspecialchars($content);
        // @高亮处理
        $content = preg_replace('/@([\x{4e00}-\x{9fa5}A-Za-z0-9_\-\.]+)：/u', '<span class="text-primary fw-bold">@$1：</span>', $content);
        // 换行处理
        return nl2br($content);
    }
    
    /**
     * 通过第三方API获取IP归属地
     * @param string $ip
     * @return string
     */
    private static function getIpLocationByApi($ip)
    {
        $url = "http://ip-api.com/json/{$ip}?lang=zh-CN";
        $resp = @file_get_contents($url);
        if ($resp) {
            $data = json_decode($resp, true);
            if ($data && $data['status'] == 'success') {
                return $data['country'] . $data['regionName'] . $data['city'];
            }
        }
        return '';
    }
}
