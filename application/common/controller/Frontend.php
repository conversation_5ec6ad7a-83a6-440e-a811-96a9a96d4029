<?php

namespace app\common\controller;

use app\common\library\Auth;
use think\Config;
use think\Controller;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Validate;

/**
 * 前台控制器基类
 */
class Frontend extends Controller
{

    /**
     * 布局模板
     * @var string
     */
    protected $layout = '';

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 权限Auth
     * @var Auth
     */
    protected $auth = null;

    public function _initialize()
    {
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
        $modulename = $this->request->module();
        $controllername = Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());

        // 检测IP是否允许
        check_ip_allowed();

        // 如果有使用模板布局
        if ($this->layout) {
            $this->view->engine->layout('layout/' . $this->layout);
        }
        $this->auth = Auth::instance();

        // token
        $token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;
        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //初始化
            $this->auth->init($token);
            //检测是否登录
            if (!$this->auth->isLogin()) {
                $this->error(__('Please login first'), 'index/user/login');
            }
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    $this->error(__('You have no permission'));
                }
            }
        } else {
            // 如果有传递token才验证是否登录状态
            if ($token) {
                $this->auth->init($token);
            }
        }

        $this->view->assign('user', $this->auth->getUser());

        // 语言检测
        $lang = $this->request->langset();
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';

        $site = Config::get("site");
        // 全局统计数
        $site['article_count'] = \app\common\model\Article::where('status', 'normal')->count();
        $site['category_count'] = \app\common\model\BlogCategory::where('status', 'normal')->count();
        $site['tag_count'] = \app\common\model\Tag::where('status', 'normal')->count();
        $this->assign('site', $site);

        // 热门分类
        $hotCategories = \app\common\model\BlogCategory::alias('c')
            ->join('article a', 'c.id = a.category_id')
            ->where('a.status', 'normal')
            ->where('c.status', 'normal')
            ->field('c.id, c.name, count(a.id) as count')
            ->group('c.id')
            ->order('count desc')
            ->limit(10)
            ->select();
        $this->assign('hotCategories', $hotCategories);
        // 热门标签
        $hotTags = \app\common\model\Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->join('article a', 'at.article_id = a.id')
            ->where('a.status', 'normal')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(at.article_id) as count')
            ->group('t.id')
            ->order('count desc')
            ->limit(10)
            ->select();
        $this->assign('hotTags', $hotTags);
        // 推荐文章（最多5个，优先推荐，不足补齐评论最多、点赞最多、最新）
        $model = new \app\common\model\Article;
        $recommendArticles = $model->where('status', 'normal')->where('is_recommend', 1)->order('createtime desc')->limit(5)->select();
        if (is_object($recommendArticles)) $recommendArticles = $recommendArticles->toArray();
        if (count($recommendArticles) < 5) {
            $ids = array_column($recommendArticles, 'id');
            $commentArticles = $model->where('status', 'normal')->whereNotIn('id', $ids)->limit(5 - count($recommendArticles))->select();
            if (is_object($commentArticles)) $commentArticles = $commentArticles->toArray();
            $recommendArticles = array_merge($recommendArticles, $commentArticles);
            $ids = array_column($recommendArticles, 'id');
            if (count($recommendArticles) < 5) {
                $likeArticles = $model->where('status', 'normal')->whereNotIn('id', $ids)->order('likes desc')->limit(5 - count($recommendArticles))->select();
                if (is_object($likeArticles)) $likeArticles = $likeArticles->toArray();
                $recommendArticles = array_merge($recommendArticles, $likeArticles);
                $ids = array_column($recommendArticles, 'id');
            }
            if (count($recommendArticles) < 5) {
                $newArticles = $model->where('status', 'normal')->whereNotIn('id', $ids)->order('createtime desc')->limit(5 - count($recommendArticles))->select();
                if (is_object($newArticles)) $newArticles = $newArticles->toArray();
                $recommendArticles = array_merge($recommendArticles, $newArticles);
            }
        }
        $this->assign('recommendArticles', $recommendArticles);
        // 广告位
        $adSidebarTop = $site['ads']['sidebar_top'] ?? ['enable' => false, 'content' => ''];
        $adSidebarBottom = $site['ads']['sidebar_bottom'] ?? ['enable' => false, 'content' => ''];
        $this->assign('adSidebarTop', $adSidebarTop);
        $this->assign('adSidebarBottom', $adSidebarBottom);

        $upload = \app\common\model\Config::upload();

        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);

        // 配置信息
        $config = [
            'site'           => array_intersect_key($site, array_flip(['name', 'cdnurl', 'version', 'timezone', 'languages'])),
            'upload'         => $upload,
            'modulename'     => $modulename,
            'controllername' => $controllername,
            'actionname'     => $actionname,
            'jsname'         => 'frontend/' . str_replace('.', '/', $controllername),
            'moduleurl'      => rtrim(url("/{$modulename}", '', false), '/'),
            'language'       => $lang
        ];
        $config = array_merge($config, Config::get("view_replace_str"));

        Config::set('upload', array_merge(Config::get('upload'), $upload));

        // 配置信息后
        Hook::listen("config_init", $config);
        // 加载当前控制器语言包
        $this->loadlang($controllername);
        $this->assign('config', $config);
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        $name = Loader::parseName($name);
        $name = preg_match("/^([a-zA-Z0-9_\.\/]+)\$/i", $name) ? $name : 'index';
        $lang = $this->request->langset();
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $lang . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 渲染配置信息
     * @param mixed $name  键名或数组
     * @param mixed $value 值
     */
    protected function assignconfig($name, $value = '')
    {
        $this->view->config = array_merge($this->view->config ? $this->view->config : [], is_array($name) ? $name : [$name => $value]);
    }

    /**
     * 刷新Token
     */
    protected function token()
    {
        $token = $this->request->param('__token__');

        //验证Token
        if (!Validate::make()->check(['__token__' => $token], ['__token__' => 'require|token'])) {
            $this->error(__('Token verification error'), '', ['__token__' => $this->request->token()]);
        }

        //刷新Token
        $this->request->token();
    }
}
